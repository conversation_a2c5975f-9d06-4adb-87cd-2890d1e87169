{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/components/memberSelection/index.vue?97e7", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/components/memberSelection/index.vue?fd6e", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/components/memberSelection/index.vue?2f8e", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/components/memberSelection/index.vue?fe32", "uni-app:///pagesHuYunIm/components/memberSelection/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/components/memberSelection/index.vue?c167", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/components/memberSelection/index.vue?ca72", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/components/memberSelection/index.vue?fbd1", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/components/memberSelection/index.vue?0461"], "names": ["props", "title", "type", "default", "dataList", "required", "id<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "phoneKey", "imgKey", "radius", "showAvatar", "isInterlock", "data", "focus", "searchStr", "scrollIntoView", "scrollIntoViewCopy", "scrollLeftObj", "oldObj", "scrollRightList", "hasData", "created", "console", "immediate", "deep", "methods", "open", "setTimeout", "Object", "uni", "createSelectorQuery", "in", "select", "boundingClientRect", "position", "initTop", "exec", "close", "focusFn", "blurFn", "search", "scrollCallback", "scrollHeight", "scrollTop", "cleanData", "list", "newList", "surplusList", "getLetter", "chooseType", "preview", "current", "urls", "chooseItem"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACa;AACyB;;;AAG1F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiG9uB;AAAA,gBACA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAG;MACAF;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;EACA;EACAU;IACA;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACAC;IACA,YACA;MAAA;IAAA,GACA;MACAA;MACA;IACA,GACA;MACAC;MACAC;IACA,EACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;QACA;UACAC;YACA;YACAC;cACAC,IACAC,sBACAC,WACAC,oDACAC;gBACA;gBACAC;gBACAC;cACA,GACAC;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;UACA;YACA;UACA;UACA;QACA;QACA,kCACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;QAAAC;MACA;QACA;MACA;MACA;QACA;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;QAAA;QACA;QACA;QACAC,yGACA,6FACA,2FACA,mGACA,+FACA,8CACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;MACA;QACA;UACA;QACA;QACA;MACA;MACAC;QACA;MACA;MACA;;MAEA;MACA;QACA;MACA;MACA;QACA;UAAA;QAAA;MACA;IACA;IACAC;MACA;MACA;QACAH;MACA;MACAA;MACA;IACA;IACAI;MACA;MACA;MACA;IACA;IACAC;MACArB;QACAsB;QACAC;MACA;IACA;IACAC;MACA/B;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC9SA;AAAA;AAAA;AAAA;AAA6hC,CAAgB,w7BAAG,EAAC,C;;;;;;;;;;;ACAjjC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/components/memberSelection/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=7e17f968&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=7e17f968&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7e17f968\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/components/memberSelection/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=7e17f968&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    mBottomPaceholder: function () {\n      return import(\n        /* webpackChunkName: \"components/m-bottom-paceholder/m-bottom-paceholder\" */ \"@/components/m-bottom-paceholder/m-bottom-paceholder.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.scrollLeftObj, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item && item.length\n    var g1 = item.length\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <uni-popup ref=\"popup\" :safe-area=\"false\" type=\"bottom\" maskBackgroundColor=\"rgba(000, 000, 000, 0.7)\">\n    <view class=\"flex_c_c next\">\n      <view class=\"top\">\n        <view class=\"icon_ text_32 bold_ top-title\">\n          <view class=\"top-title-icon\"></view>\n          <view class=\"flex1 icon_\">{{ title }}</view>\n          <view class=\"top-title-icon\" @click=\"close\">\n            <image\n              class=\"img\"\n              src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjUgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMS45NzkgMTAyNEMyMjkuNjg5IDEwMjQgMCA3OTQuMzEgMCA1MTEuOTc5IDAgMjI5LjY4OSAyMjkuNjkgMCA1MTEuOTc5IDBzNTExLjk3OCAyMjkuNjkgNTExLjk3OCA1MTEuOTc5QzEwMjQgNzk0LjMxIDc5NC4zMSAxMDI0IDUxMS45OCAxMDI0em0wLTk0NS41MDZjLTIzOS4wMTcgMC00MzMuNDg1IDE5NC40NjgtNDMzLjQ4NSA0MzMuNDQyIDAgMjM5LjAxNyAxOTQuNDY4IDQzMy41MjcgNDMzLjQ4NSA0MzMuNTI3IDIzOS4wMTcgMCA0MzMuNDg0LTE5NC40NjcgNDMzLjQ4NC00MzMuNTI3IDAtMjM4Ljk3NC0xOTQuNDI1LTQzMy40NDItNDMzLjQ4NC00MzMuNDQyeiIgZmlsbD0iIzUxNTE1MSIvPjxwYXRoIGQ9Ik01NjEuNjgyIDUxMS45NzlsMTUxLjc1LTE1Mi4xNzZhMzUuOTQ2IDM1Ljk0NiAwIDAgMCAwLTUwLjcyNSAzNS42OSAzNS42OSAwIDAgMC01MC41OTggMGwtMTUxLjc1IDE1Mi4yMTgtMTUxLjc1LTE1Mi4xNzVhMzUuNjkgMzUuNjkgMCAwIDAtNTAuNTk2IDAgMzUuOTQ2IDM1Ljk0NiAwIDAgMCAwIDUwLjcyNUw0NjAuNDg3IDUxMi4wMmwtMTUxLjc1IDE1Mi4xMzNhMzUuNzc2IDM1Ljc3NiAwIDEgMCA1MC41OTggNTAuNzI1bDE1MS43NS0xNTIuMTc1IDE1MS43NDkgMTUyLjE3NWEzNS43NzYgMzUuNzc2IDAgMSAwIDUwLjU5Ny01MC43MjVMNTYxLjY4MSA1MTEuOTh6IiBmaWxsPSIjNTE1MTUxIi8+PC9zdmc+\"\n              mode=\"aspectFill\"\n            ></image>\n          </view>\n        </view>\n        <view class=\"icon_ search\">\n          <view class=\"icon_ z_index2\" v-if=\"!focus & !searchStr\">\n            <view class=\"search-icon\">\n              <image\n                class=\"img\"\n                src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTQ2OS4zMzMgMEMyMDkuMDY3IDAgMCAyMDkuMDY3IDAgNDY5LjMzM3MyMDkuMDY3IDQ2OS4zMzQgNDY5LjMzMyA0NjkuMzM0UzkzOC42NjcgNzI5LjYgOTM4LjY2NyA0NjkuMzMzIDcyOS42IDAgNDY5LjMzMyAwem0wIDg1My4zMzNjLTIxMy4zMzMgMC0zODQtMTcwLjY2Ni0zODQtMzg0czE3MC42NjctMzg0IDM4NC0zODQgMzg0IDE3MC42NjcgMzg0IDM4NC0xNzAuNjY2IDM4NC0zODQgMzg0eiIgZmlsbD0iIzliOWI5YiIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pMS4xMTdjM2E4MVdwVG9pVyIgY2xhc3M9InNlbGVjdGVkIi8+PHBhdGggZD0iTTczOC4xMzMgNzQyLjRjMTcuMDY3LTE3LjA2NyA0Mi42NjctMTcuMDY3IDU5LjczNCAwbDIwOS4wNjYgMjAwLjUzM2MxNy4wNjcgMTcuMDY3IDE3LjA2NyA0Mi42NjcgMCA1OS43MzQtMTcuMDY2IDE3LjA2Ni00Mi42NjYgMTcuMDY2LTU5LjczMyAwTDczOC4xMzMgODAyLjEzM2MtMTcuMDY2LTE3LjA2Ni0xNy4wNjYtNDIuNjY2IDAtNTkuNzMzeiIgZmlsbD0iIzliOWI5YiIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pNC4xMTdjM2E4MVdwVG9pVyIgY2xhc3M9InNlbGVjdGVkIi8+PC9zdmc+\"\n                mode=\"aspectFill\"\n              ></image>\n            </view>\n            <view class=\"text_32 search-text\">搜索</view>\n          </view>\n\n          <view class=\"search-input\">\n            <input\n              @input=\"search\"\n              v-model=\"searchStr\"\n              :focus=\"focus\"\n              @focus=\"focusFn\"\n              @blur=\"blurFn\"\n              :adjust-position=\"false\"\n              maxlength=\"50\"\n            />\n          </view>\n        </view>\n      </view>\n      <view class=\"flex1 next-list\">\n        <scroll-view\n          @scroll=\"scrollCallback\"\n          class=\"next-scroll-left\"\n          scroll-y=\"true\"\n          :scroll-with-animation=\"true\"\n          :scroll-into-view=\"scrollIntoView\"\n        >\n          <view class=\"left-list\" v-for=\"(item, index) of scrollLeftObj\" :key=\"index\" :id=\"index != '#' ? index : 'BOTTOM'\">\n            <view :id=\"`item${index}`\" class=\"left-item-title\" v-if=\"item && item.length\">{{ index === 'no' ? '#' : index }}</view>\n            <view class=\"left-item-card\" v-for=\"(mess, inx) in item\" :key=\"inx\" @click.stop=\"chooseItem(mess)\">\n              <view v-if=\"showAvatar\">\n                <image\n                  :style=\"'border-radius:' + radius\"\n                  class=\"left-item-card-img img-info\"\n                  :src=\"mess[imgKey]\"\n                  v-if=\"mess[imgKey]\"\n                  mode=\"aspectFill\"\n                  @click.stop=\"preview(mess[imgKey])\"\n                ></image>\n              </view>\n              <view class=\"left-item-card-info\" :style=\"inx < item.length - 1 ? 'border-bottom: solid #ededed 1rpx;' : ''\">\n                <view class=\"left-item-card-name\">{{ mess[nameKey] || '' }}</view>\n              </view>\n            </view>\n          </view>\n\n          <view class=\"flex_c_c no-data\" v-if=\"!hasData\">\n            <view class=\"no-data-img\">\n              <image\n                class=\"img\"\n                src=\"data:image/svg+xml;base64,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\"\n                mode=\"aspectFill\"\n              ></image>\n            </view>\n            <view class=\"text_26 color__\">没查询到人员</view>\n          </view>\n          <view style=\"height: 180rpx\"></view>\n          <m-bottom-paceholder></m-bottom-paceholder>\n        </scroll-view>\n        <view class=\"next-scroll-right\" v-if=\"hasData\">\n          <view\n            class=\"next-scroll-right-name\"\n            :class=\"{ 'next-scroll-right-select': item == scrollIntoViewCopy }\"\n            v-for=\"(item, index) in scrollRightList\"\n            :key=\"index\"\n            @click.stop=\"chooseType(item)\"\n          >\n            {{ item === 'no' ? '#' : item }}\n          </view>\n        </view>\n      </view>\n    </view>\n  </uni-popup>\n</template>\n\n<script>\nconst position = {}\nexport default {\n  props: {\n    title: {\n      type: String,\n      default: ''\n    },\n    dataList: {\n      type: Array,\n      required: true,\n      default: () => {\n        return []\n      }\n    },\n    //显示的主键key值\n    idKey: {\n      type: String,\n      default: 'id'\n    },\n    nameKey: {\n      type: String,\n      default: 'name'\n    },\n    phoneKey: {\n      type: String,\n      default: 'id'\n    },\n    imgKey: {\n      type: String,\n      default: 'member_avatar'\n    },\n    radius: {\n      type: String,\n      default: '10rpx'\n    },\n    showAvatar: {\n      type: Boolean,\n      default: true\n    },\n    isInterlock: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      focus: false,\n\n      searchStr: '',\n      scrollIntoView: '',\n      scrollIntoViewCopy: '',\n      scrollLeftObj: {},\n      oldObj: {},\n      scrollRightList: [],\n      hasData: true\n    }\n  },\n  created() {\n    console.log('🚀 ~ created ~  this.$watch:', this.$watch)\n    this.$watch(\n      () => this.dataList,\n      (newList) => {\n        console.log('🚀 ~ created ~ newList:', newList)\n        if (newList && newList.length) this.cleanData(newList)\n      },\n      {\n        immediate: true,\n        deep: true\n      }\n    )\n  },\n  methods: {\n    open() {\n      this.$refs.popup.open()\n      if (this.isInterlock) {\n        this.$nextTick(() => {\n          setTimeout(() => {\n            let initTop = 0\n            Object.keys(this.scrollRightList).map((key) => {\n              uni\n                .createSelectorQuery()\n                .in(this)\n                .select(`#item${this.scrollRightList[key]}`)\n                .boundingClientRect((res) => {\n                  const { top } = res\n                  position[this.scrollRightList[key]] = top - initTop\n                  initTop = position[this.scrollRightList[0]]\n                })\n                .exec()\n            })\n          }, 300)\n        })\n      }\n    },\n    close() {\n      this.$refs.popup.close()\n    },\n    focusFn() {\n      this.focus = true\n    },\n    blurFn() {\n      this.focus = false\n    },\n    search() {\n      this.focus = true\n      if (this.searchStr) {\n        let has = false\n        this.scrollLeftObj = JSON.parse(JSON.stringify(this.oldObj))\n        for (let i in this.scrollLeftObj) {\n          this.scrollLeftObj[i] = this.scrollLeftObj[i].filter((item) => {\n            return item[this.nameKey].indexOf(this.searchStr) != -1 || `${item[this.phoneKey]}`.indexOf(this.searchStr) != -1\n          })\n          if (this.scrollLeftObj[i].length) has = true\n        }\n        if (has) this.hasData = true\n        else this.hasData = false\n      } else {\n        this.hasData = true\n        this.scrollLeftObj = JSON.parse(JSON.stringify(this.oldObj))\n      }\n    },\n    scrollCallback(e) {\n      const { detail } = e\n      const { scrollTop, scrollHeight } = detail\n      if (this.scrollIntoView === 'TOP') {\n        this.scrollIntoView = ''\n      }\n      if (this.isInterlock) {\n        for (let key in position) {\n          if (position[key] - scrollTop > 0 && position[key] - scrollTop <= scrollHeight) {\n            this.scrollIntoViewCopy = key\n            return\n          }\n        }\n      }\n    },\n    scrollTop() {\n      this.scrollIntoView = 'TOP'\n    },\n    cleanData(list) {\n      this.scrollRightList = this.getLetter()\n      let newList = []\n      list.forEach((res) => {\n        let firsfirs = res.letter\n        if (!newList[firsfirs]) newList[firsfirs] = []\n        newList[firsfirs].push({\n          [this.idKey]: res[this.idKey] || '',\n          [this.nameKey]: res[this.nameKey],\n          [this.phoneKey]: res[this.phoneKey] || '',\n          [this.imgKey]: res[this.imgKey] || '',\n          ['mobile']: res.mobile\n        })\n      })\n      this.scrollRightList.forEach((t) => {\n        if (newList[t]) {\n          this.$set(this.scrollLeftObj, t, newList[t])\n        } else {\n          this.$set(this.scrollLeftObj, t, [])\n        }\n      })\n      let surplusList = []\n      for (var i in newList) {\n        let han = this.scrollRightList.find((v) => {\n          return v == i\n        })\n        if (!han) surplusList.push(newList[i])\n      }\n      surplusList.forEach((item) => {\n        this.scrollLeftObj['no'] = this.scrollLeftObj['no'].concat(item)\n      })\n      this.oldObj = JSON.parse(JSON.stringify(this.scrollLeftObj))\n\n      // 过滤不存在的关键索引\n      const existList = Object.keys(this.scrollLeftObj).filter((key) => {\n        return this.scrollLeftObj[key].length\n      })\n      this.scrollRightList = this.scrollRightList.filter((key) => {\n        return existList.some((k) => k === key)\n      })\n    },\n    getLetter() {\n      let list = []\n      for (var i = 0; i < 26; i++) {\n        list.push(String.fromCharCode(65 + i))\n      }\n      list.push('no')\n      return list\n    },\n    chooseType(item) {\n      if (item == 'no') item = 'BOTTOM'\n      this.scrollIntoView = item\n      this.scrollIntoViewCopy = item\n    },\n    preview(img) {\n      uni.previewImage({\n        current: 0,\n        urls: [img]\n      })\n    },\n    chooseItem(item) {\n      console.log(item)\n      this.$emit('itemclick', item)\n      this.$refs.popup.close()\n    }\n  }\n}\n</script>\n<style>\n/deep/ ::-webkit-scrollbar {\n  width: 0;\n  height: 0;\n  color: transparent;\n  display: none;\n}\n</style>\n<style lang=\"scss\" scoped>\n.next {\n  position: relative;\n  width: 100%;\n  height: 82vh;\n  background-color: #fff;\n  overflow: hidden;\n  border-radius: 20rpx 20rpx 0 0;\n  .top {\n    width: 100%;\n    height: 250rpx;\n    .top-title {\n      width: calc(100% - 60rpx);\n      height: 120rpx;\n      margin: 0 auto;\n      .top-title-icon {\n        width: 44rpx;\n        height: 44rpx;\n      }\n    }\n\n    .search {\n      position: relative;\n      width: calc(100% - 40rpx);\n      height: 80rpx;\n      margin: 0 auto;\n      border-radius: 14rpx;\n      background-color: #ededed;\n      .search-input {\n        box-sizing: border-box;\n        padding: 0 20rpx;\n        position: absolute;\n        z-index: 3;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        input {\n          width: 100%;\n          height: 100%;\n          text-align: center;\n        }\n      }\n      .search-icon {\n        width: 34rpx;\n        height: 34rpx;\n        margin-right: 16rpx;\n      }\n      .search-text {\n        color: #9b9b9b;\n      }\n    }\n  }\n}\n\n.next-list {\n  position: relative;\n  width: 100%;\n  height: 0;\n  box-sizing: border-box;\n  .next-scroll-left {\n    height: 100%;\n\n    .left-list {\n      height: auto;\n\n      .left-item-title {\n        width: calc(100% - 24rpx);\n        height: 60rpx;\n        padding-left: 24rpx;\n        text-align: left;\n        line-height: 60rpx;\n        font-size: 30rpx;\n        color: #666666;\n      }\n\n      .left-item-card {\n        width: 100%;\n        height: 112rpx;\n        background-color: #ffffff;\n        box-sizing: border-box;\n        padding-left: 24rpx;\n        display: flex;\n        align-items: center;\n        justify-content: flex-start;\n\n        .left-item-card-img {\n          width: 80rpx;\n          min-width: 80rpx;\n          height: 80rpx;\n          background-color: #cfcfcf;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 36rpx;\n          font-weight: bold;\n          color: #ffffff;\n          overflow: hidden;\n        }\n\n        .img-info {\n          background: none;\n          border: solid #f0f0f0 1rpx;\n        }\n\n        .left-item-card-info {\n          width: 100%;\n          margin-left: 20rpx;\n          height: 100%;\n          display: flex;\n          align-items: flex-start;\n          justify-content: center;\n          flex-direction: column;\n\n          .left-item-card-name {\n            font-size: 30rpx;\n            line-height: 30rpx;\n            color: #333333;\n          }\n\n          .left-item-card-phone {\n            margin-top: 14rpx;\n            font-size: 28rpx;\n            line-height: 28rpx;\n            color: #999999;\n          }\n        }\n      }\n    }\n  }\n\n  .next-scroll-right {\n    position: absolute;\n    right: 0rpx;\n    top: 40%;\n    transform: translateY(-47%);\n    z-index: 999 !important;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-direction: column;\n\n    .next-scroll-right-top {\n      width: 32rpx;\n      height: 32rpx;\n      margin-right: 14rpx;\n      z-index: 999 !important;\n    }\n\n    .next-scroll-right-name {\n      width: 32rpx;\n      padding-right: 14rpx;\n      height: 28rpx;\n      font-size: 22rpx;\n      color: #515151;\n      line-height: 28rpx;\n      margin-top: 8rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .next-scroll-right-select {\n      padding: 0;\n      margin-right: 14rpx;\n      width: 28rpx;\n      height: 28rpx;\n      border-radius: 50%;\n      background: #0cbf5e;\n      color: #ffffff;\n    }\n  }\n\n  .no-data {\n    width: 100%;\n    .no-data-img {\n      width: 200rpx;\n      height: 200rpx;\n      margin-top: 100rpx;\n      margin-bottom: 20rpx;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755156565557\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=7e17f968&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=7e17f968&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755156565995\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}