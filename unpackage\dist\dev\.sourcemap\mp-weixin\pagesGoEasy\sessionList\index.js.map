{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/sessionList/index.vue?e6fd", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/sessionList/index.vue?d2a5", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/sessionList/index.vue?69d2", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/sessionList/index.vue?160e", "uni-app:///pagesGoEasy/sessionList/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/sessionList/index.vue?924f", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/sessionList/index.vue?872b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "unreadTotal", "list", "issound", "currentUser", "onShow", "onLoad", "onUnload", "onHide", "computed", "statusBar", "customBar", "options", "text", "style", "backgroundColor", "methods", "to", "formatDate", "onItem", "receiveCallback", "console", "init", "bindClick", "loadConversations", "topConversation", "uni", "title", "icon", "deleteConversation", "content", "success", "API_huihua_config", "connect", "clearInterval", "url", "loadData", "self", "listUser", "res", "err", "userArr", "userItem", "<PERSON><PERSON>", "connectMqtt", "userInfo", "userId", "nickname", "channelCode", "avatar", "wsUrl", "token", "opts", "protocolVersion", "clientId", "username", "password", "clean", "rejectUnauthorized", "on", "chatMsg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,4TAEN;AACP,KAAK;AACL;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACkJ/tB;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MACA;QACA,QACA;UACAC;UACAC;YACAC;UACA;QACA,GACA;UACAF;UACAC;YACAC;UACA;QACA,EACA;MACA;IACA;EACA;IACAH;MACA;QACA,QACA;UACAC;UACAC;YACAC;UACA;QACA,GACA;UACAF;UACAC;YACAC;UACA;QACA,EACA;MACA;IACA;EAAA,EACA;EACAC;IACAC;IACAC;IACAC;MACA;IACA;IACAC;MACAC;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACAH;MACA;IACA;IACAI;MACA;MACA;MACAC;QACAC;QACAC;MACA;IACA;IACAC;MACAH;QACAI;QACAC;MACA;IACA;IACAC;IACAC;MACA;MACAC;MACAR;QACAS;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACArC;gBAAA;gBAAA,OACAsC;cAAA;gBAAA;gBAAA;gBAAAC;gBAAAC;gBAEA;kBACA;;kBAEA;oBACAC;oBACApB;oBACA;sBACAqB;sBACAL;oBACA;oBACA;sBACAE;oBACA;oBACA;kBACA;;kBACA;kBACAI;gBACA;gBACA;kBACAtB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAuB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAP;gBAEAQ;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACAC;kBACA;kBACA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACArB;gBACAA,gBACAsB;kBACA;kBACAzB;kBACAG;oBACAA;oBACAhB;kBACA;kBACAgB;oBACA;sBACAhB;oBACA;kBACA;gBACA,GACAsC;kBACA;gBAAA,CACA,EACAA;kBACAtC;kBACA;gBACA,GACAsC;kBACAtC;kBACA;gBACA,GACAsC;kBACA;kBACA;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACA;wBACAvB;wBACAA;wBACAA;sBACA;oBACA;oBACA;oBACAhB;kBACA;;kBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtWA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/sessionList/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesGoEasy/sessionList/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=cd712850&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=cd712850&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"cd712850\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/sessionList/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=cd712850&scoped=true&\"", "var components\ntry {\n  components = {\n    uniSwipeAction: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-swipe-action/uni-swipe-action\" */ \"@/components/uni-swipe-action/uni-swipe-action.vue\"\n      )\n    },\n    uniSwipeActionItem: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-swipe-action-item/uni-swipe-action-item\" */ \"@/components/uni-swipe-action-item/uni-swipe-action-item.vue\"\n      )\n    },\n    mLine: function () {\n      return import(\n        /* webpackChunkName: \"components/m-line/m-line\" */ \"@/components/m-line/m-line.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map([].concat(_vm.list), function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.options(item)\n          var m1 = _vm.formatDate(item.lastMessage.timestamp, \"timestamp\")\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.issound = !_vm.issound\n    }\n    _vm.e1 = function ($event) {\n      _vm.issound = false\n    }\n    _vm.e2 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      return _vm.bindClick($event, item)\n    }\n    _vm.e3 = function ($event, item) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        item = _temp4.item\n      var _temp3, _temp4\n      return _vm.onItem(item)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"flex_c_c page\">\r\n\t\t<view class=\"navigationBar-box\">\r\n\t\t\t<view :style=\"{ height: statusBar - 8 + 'px' }\"></view>\r\n\t\t\t<view class=\"flex_r fa_c fj_b navigationBar\" :style=\"{ height: customBar - statusBar + 'px' }\">\r\n\t\t\t\t<view class=\"navigationBar-icon\" @click=\"to()\">\r\n\t\t\t\t\t<image\r\n\t\t\t\t\t\tclass=\"img\"\r\n\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyNC4yMTIgNTExLjgwNkw3ODcuODkgNzMuMDgzYzE2LjE5NC0xNi42MyAxNi4xOTQtNDMuOTc1IDAtNjAuNjA1LTE2LjE5NS0xNi42My00Mi40OTYtMTYuNjMtNTguNjE0IDBMMjM1Ljc1IDQ3OS4zNmMtOC42NDcgOC45Ny0xMi4zNDUgMjAuOTM1LTExLjcxOSAzMi40NDYtLjY0NSAxMS45MDggMy4wNzIgMjMuODc0IDExLjcyIDMyLjgyNGw0OTMuNTA2IDQ2Ni44ODNjMTYuMTE4IDE2LjY1IDQyLjQzOCAxNi42NSA1OC42MTQgMCAxNi4xOTQtMTcuMDg1IDE2LjE5NC00My45NzUgMC02MC42MDVMMzI0LjIxIDUxMS44MDYiIGZpbGw9IiMxZDFkMWQiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTguMmM2YTNhODFJY0NLRlYiIGNsYXNzPSJzZWxlY3RlZCIvPjwvc3ZnPg==\"\r\n\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"navigationBar-text bold_\" style=\"margin-left: 30rpx\">交流区</view>\r\n\t\t\t\t<view class=\"navigationBar-icon size_white navigationBar-icon2\" @click=\"issound = !issound\">\r\n\t\t\t\t\t<image\r\n\t\t\t\t\t\tclass=\"img\"\r\n\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMS42OCA2My42OGMtMjQ3Ljc3NiAwLTQ0OC41NzYgMjAwLjgtNDQ4LjU3NiA0NDguNTc2czIwMC44IDQ0OC42NCA0NDguNTc2IDQ0OC42NGMyNDcuODA4IDAgNDQ4LjYwOC0yMDAuOCA0NDguNjA4LTQ0OC42NEM5NjAuMjU2IDI2NC41MTIgNzU5LjQ1NiA2My42OCA1MTEuNjggNjMuNjh6bTAgODMyYy0yMTEuNzEyIDAtMzg0LTE3Mi4yNTYtMzg0LTM4NHMxNzIuMjg4LTM4NCAzODQtMzg0YzIxMS43NDQgMCAzODQuMDMyIDE3Mi4yNTYgMzg0LjAzMiAzODRzLTE3Mi4zMiAzODQtMzg0LjAzMiAzODR6bTI3LjIzMi01ODguOTI4YzAtMTcuNi0xMy41MDQtMzItMzAuMDE2LTMycy0zMC4wMTYgMTQuNC0zMC4wMTYgMzJWNDc2LjczNmMwIDIuNTkyLTIuMDggNC43MDQtNC42NzIgNC43MDRoLTE2Ny42OGMtMTcuNiAwLTMyIDEzLjIxNi0zMiAyOS4zNDQgMCAxNi4xNiAxNC40IDI5LjM3NiAzMiAyOS4zNzZoMTY4LjQxNmMyLjE0NCAwIDMuOTM2IDIuMDggMy45MzYgNC42NzJ2MTcxLjg0YzAgMTcuNiAxMy41MDQgMzIgMzAuMDE2IDMyczMwLjAxNi0xNC40IDMwLjAxNi0zMlY1NzkuMDA4di0zNS4zOTJjMC0xLjg1NiAxLjYtMy4zOTIgMy41NTItMy4zOTJINzE2LjgzMmMxNy42IDAgMzItMTMuMjE2IDMyLTI5LjM3NiAwLTE2LjEyOC0xNC40LTI5LjM0NC0zMi0yOS4zNDRINTQzLjJjLTIuMzY4IDAtNC4yODgtMi41OTItNC4yODgtNS43MjhWMzA2Ljc1MnoiIGZpbGw9IiMyYzJjMmMiLz48L3N2Zz4=\"\r\n\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t></image>\r\n\r\n\t\t\t\t\t<view :class=\"issound ? 'dropDown active' : 'dropDown'\" :hover-stop-propagation=\"true\">\r\n\t\t\t\t\t\t<view class=\"flex_r fa_c list\" @tap.stop=\"to('/pagesGoEasy/group_create/index')\">\r\n\t\t\t\t\t\t\t<view class=\"icon_ list-icon\">\r\n\t\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\t\tclass=\"img\"\r\n\t\t\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMi4xMyAyNy42NjdDMjI5LjIxOSAyNy42NjcgMCAyMjguMTcgMCA0NzUuNjg0YzAgMTU0LjQ5OCA4OS40MjggMjkwLjkxMyAyMjUuNzQ4IDM3MS40OSAzNi42MTMgMjEuNy0zNi45MDUgMTUxLjE2OCAxNC4wMzUgMTUxLjE2OCAyMi44NjEgMCAyMTMuNzMzLTc0LjY0MyAyNzIuMTk4LTc0LjY0MyAyODIuNzY0IDAgNTEyLjEyMy0yMDAuNTAyIDUxMi4xMjMtNDQ4LjAxMy4xNDctMjQ3LjUxNS0yMjkuMDcyLTQ0OC4wMi01MTEuOTc0LTQ0OC4wMnptMTQ0LjU2IDUxMi42NzlINTM5LjYxOHYxMTcuMDMxYzAgMTUuMTg0LTEyLjMwMiAyNy4zNDMtMjcuMzUxIDI3LjM0My0xNS4wNDQgMC0yNy4zNDQtMTIuMjk3LTI3LjM0NC0yNy4zNDNWNTQwLjM0NmgtMTE3LjA3Yy0xNS4xOTcgMC0yNy4zNTItMTIuMjk3LTI3LjM1Mi0yNy4zNDJzMTIuMzAyLTI3LjM0MyAyNy4zNTEtMjcuMzQzaDExNy4wN1YzNjguNjNjMC0xNS4xODQgMTIuMzAyLTI3LjMzNiAyNy4zNDUtMjcuMzM2IDE1LjA1MSAwIDI3LjM1MSAxMi4yOTcgMjcuMzUxIDI3LjMzNlY0ODUuNjZoMTE3LjA3YzE1LjE5NyAwIDI3LjM1MiAxMi4yOTcgMjcuMzUyIDI3LjM0My4wMDEgMTUuMDQ1LTEyLjMgMjcuMzQyLTI3LjM1MiAyNy4zNDJ6bTAgMCIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pNC4zYmU2M2E4MXBQN3hlayIgY2xhc3M9InNlbGVjdGVkIiBmaWxsPSIjZmZmIi8+PC9zdmc+\"\r\n\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex1 size_white list-title\">发起群聊</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex_r fa_c list\">\r\n\t\t\t\t\t\t\t<view class=\"icon_ list-icon\">\r\n\t\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\t\tclass=\"img\"\r\n\t\t\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,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\"\r\n\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex1 size_white list-title\">扫一扫</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"flex_r fa_c list\"\r\n\t\t\t\t\t\t\**********=\"\r\n\t\t\t\t\t\t\t\tto('/pagesGoEasy/admin/addressBook', {\r\n\t\t\t\t\t\t\t\t\tshowContacts: huihua_config.allow_add_friends\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"icon_ list-icon\">\r\n\t\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\t\tclass=\"img\"\r\n\t\t\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDExMDMgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTkxOS4yMTcgNzUzLjg4djIxNC4yMTRsLTIxNC40NDktMTI4LjY5Yy0yOS42NzYgNC44NjQtNjAuMiA3LjY4LTkxLjUxIDcuNjgtMjcwLjMzNiAwLTQ4OS40OTItMTg5LjYzNy00ODkuNDkyLTQyMy41NDJDMTIzLjc2NiAxODkuNjE3IDM0Mi45MjIgMCA2MTMuMjU4IDBjMjcwLjM1NiAwIDQ4OS41MTEgMTg5LjYxNyA0ODkuNTExIDQyMy41NDIgMCAxMzMuNjcyLTcxLjc1OSAyNTIuNzEyLTE4My41NTIgMzMwLjMzOXpNNDYwLjMwNyA1NzQuOGgzMDUuOTJjMTYuOTE2IDAgMzAuNjAzLTEzLjUyOSAzMC42MDMtMzAuMjQ4IDAtMTYuNzE4LTEzLjY4Ny0zMC4yNDctMzAuNjAyLTMwLjI0N2gtMzA1LjkyYy0xNi44OTYgMC0zMC42MDIgMTMuNTI5LTMwLjYwMiAzMC4yNDcgMCAxNi43MiAxMy43MDYgMzAuMjQ4IDMwLjYwMiAzMC4yNDh6TTgyNy40MzIgMzMyLjc4SDM5OS4xMDRjLTE2Ljg5NiAwLTMwLjU4MiAxMy41NDktMzAuNTgyIDMwLjI0OHMxMy42ODYgMzAuMjQ3IDMwLjU4MiAzMC4yNDdoNDI4LjMyN2MxNi44OTYgMCAzMC41ODMtMTMuNTQ4IDMwLjU4My0zMC4yNDcgMC0xNi43LTEzLjY4Ny0zMC4yNDgtMzAuNTgzLTMwLjI0OHpNMjE2LjYxNSA3MjguNjE1YzEzMy43NSAxNjIuMzgzIDMzNi4yMjcgMTM4Ljg1IDM3My41MDQgMTM0LjcxNS0xLjU3NS4xNTggMi45MzUtMS4xODEgNC40MTEtMS43NzIgNC44ODQgMTEuMDA4IDExLjEwNyAyMS4zNDcgMTguNDEzIDMwLjgxOS01MS44MyAyMS4xNjktMTA5LjU4OCAzMy43MzMtMTcwLjcxMyAzNS40NDYgMCAwLTg3Ljc4OCA5MC4xOS0yMDYuMDYgOTYuMTU3LS4zMzUuMDItLjY1LjAyLS45ODUuMDItMTQuNTcyIDAtMjMuMTc4LTE2LjUyMi0xNC40MTUtMjguMzc3IDE3LjExMy0yMy4xNzcgMzQuNzU3LTU5LjU1IDIyLjk2Mi0xMDIuMzJDOTkuNjIzIDgzNS43NDEgMCA3MTIuMjUgMCA1NjkuMTg1YzAtNzQuMDgyIDI2Ljc0Mi0xNDIuOTI2IDcyLjUyNy0yMDAuMDkzIDYuOTkgOS44ODUgMTIuNDA2IDI0LjQ1OCAyNC4yNiAyNi44OC0yMC44MzQgMTQ4LjY5NyAzNS42NjMgMjMwLjQ0IDExOS44MjggMzMyLjY0MnoiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTcuM2JlNjNhODFwUDd4ZWsiIGNsYXNzPSJzZWxlY3RlZCIgZmlsbD0iI2ZmZiIvPjwvc3ZnPg==\"\r\n\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex1 size_white list-title\">通讯录</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<scroll-view class=\"conversations flex1\" scroll-y=\"true\" @click=\"issound = false\">\r\n\t\t\t<view v-if=\"list.length > 0\">\r\n\t\t\t\t<uni-swipe-action>\r\n\t\t\t\t\t<uni-swipe-action-item\r\n\t\t\t\t\t\tclass=\"scroll-item-box\"\r\n\t\t\t\t\t\t:class=\"{ scroll_item_box: item.top }\"\r\n\t\t\t\t\t\t:right-options=\"options(item)\"\r\n\t\t\t\t\t\t@click=\"bindClick($event, item)\"\r\n\t\t\t\t\t\tv-for=\"(item, index) in [...list]\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"flex_r scroll-item\" @click=\"onItem(item)\">\r\n\t\t\t\t\t\t\t<view class=\"item-head\">\r\n\t\t\t\t\t\t\t\t<view class=\"item-head-img\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"item.data.avatar\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item-head_unread\" v-if=\"item.unread\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex_c fj_b scroll-item_info\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex_r\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"text_34 flex1\">{{ item.data.name }}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"text_26\" style=\"color: #cccccc\">\r\n\t\t\t\t\t\t\t\t\t\t{{ formatDate(item.lastMessage.timestamp, 'timestamp') }}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text_28 nowrap_ item_info-text\">\r\n\t\t\t\t\t\t\t\t\t<template v-if=\"!item.lastMessage.recalled\">\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.lastMessage.senderId == currentUser.member_id\">我：</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t{{ item.type === 'group' ? item.lastMessage.senderData.name : item.data.name }}：\r\n\t\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"nowrap_\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"item.lastMessage.type === 'text' || item.lastMessage.type === 'text_quote'\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{{ item.lastMessage.payload.text }}\r\n\t\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'video'\">[视频消息]</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'audio'\">[语音消息]</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'image' || item.lastMessage.type === 'image_transmit'\">\r\n\t\t\t\t\t\t\t\t\t\t\t[图片消息]\r\n\t\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'emoji_pack'\">[表情包]</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'red_envelope'\">[蝌蚪红包]</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'map'\">[位置信息]</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'group_notice'\">[群公告]</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'update_group_name'\">[修改群名称]</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'article'\">[文章分享]</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'share_SBCF'\">[商家分享]</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"item.lastMessage.type === 'share_mall'\">[商品分享]</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-else>[新信息]</text>\r\n\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t<template class=\"\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t<text>\r\n\t\t\t\t\t\t\t\t\t\t\t{{\r\n\t\t\t\t\t\t\t\t\t\t\t\titem.lastMessage.recaller.id === currentUser.member_id\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? '你'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: item.lastMessage.recaller.data.name\r\n\t\t\t\t\t\t\t\t\t\t\t}}撤回了一条消息\r\n\t\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"m-line\">\r\n\t\t\t\t\t\t\t\t\t<m-line color=\"#ededed\" length=\"100%\" :hairline=\"true\"></m-line>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</uni-swipe-action-item>\r\n\t\t\t\t</uni-swipe-action>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex_c_c\" v-else>\r\n\t\t\t\t<view class=\"no-conversation\">\r\n\t\t\t\t\t<image\r\n\t\t\t\t\t\tclass=\"img\"\r\n\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,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\"\r\n\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"color__\">暂无会话记录</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t</view>\r\n</template>\r\n<script>\r\nimport { 自己的信息, 会话列表数据 } from '@/TEST/index.js'\r\nimport { connectGoEasy, getStatusBar, to, formatDate } from '@/utils/index.js'\r\nimport { mapState } from 'vuex'\r\n\r\nexport default {\r\n\tname: 'conversation',\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tunreadTotal: 0,\r\n\t\t\tlist: [],\r\n\t\t\tissound: false,\r\n\t\t\tcurrentUser: 自己的信息\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t// this.loadData()\r\n\t\tthis.connectMqtt()\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.init()\r\n\t},\r\n\tonUnload() {\r\n\t\tthis.issound = false\r\n\t},\r\n\tonHide() {\r\n\t\tthis.issound = false\r\n\t},\r\n\tcomputed: {\r\n\t\t...mapState({\r\n\t\t\tstatusBar: (state) => state.StatusBar.statusBar,\r\n\t\t\tcustomBar: (state) => state.StatusBar.customBar,\r\n\t\t\toptions() {\r\n\t\t\t\treturn (item) => {\r\n\t\t\t\t\treturn [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\ttext: '不显示',\r\n\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\tbackgroundColor: '#ffaa00'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\ttext: item.top ? '取消置顶' : '置顶',\r\n\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\tbackgroundColor: item.top ? '#999' : '#F56C6C'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}),\r\n\t\toptions() {\r\n\t\t\treturn (item) => {\r\n\t\t\t\treturn [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '不显示',\r\n\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\tbackgroundColor: '#ffaa00'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: item.top ? '取消置顶' : '置顶',\r\n\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\tbackgroundColor: item.top ? '#999' : '#F56C6C'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tto,\r\n\t\tformatDate,\r\n\t\tonItem(item) {\r\n\t\t\tto(`/pagesGoEasy/chat_page/index?groupId=${item.groupId}`)\r\n\t\t},\r\n\t\treceiveCallback() {\r\n\t\t\tconsole.log('addad')\r\n\t\t},\r\n\t\t// 初始化\r\n\t\tasync init() {\r\n\t\t\tthis.loadConversations() //加载会话列表\r\n\t\t},\r\n\t\tbindClick(e, item) {\r\n\t\t\tif (e.index) {\r\n\t\t\t\tthis.topConversation(item)\r\n\t\t\t} else {\r\n\t\t\t\tthis.deleteConversation(item)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 加载最新的会话列表\r\n\t\tloadConversations() {\r\n\t\t\tconsole.log(会话列表数据)\r\n\t\t\tthis.list = 会话列表数据\r\n\t\t},\r\n\t\ttopConversation(conversation) {\r\n\t\t\t//会话置顶\r\n\t\t\tlet description = conversation.top ? '取消置顶' : '置顶'\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: description + '成功',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t},\r\n\t\tdeleteConversation(conversation) {\r\n\t\t\tuni.showModal({\r\n\t\t\t\tcontent: '确认删除这条会话吗？',\r\n\t\t\t\tsuccess: (res) => {}\r\n\t\t\t})\r\n\t\t},\r\n\t\tAPI_huihua_config() {},\r\n\t\tconnect(item) {\r\n\t\t\tthis.mqttClient.end()\r\n\t\t\tclearInterval(this.mqttPingIntetval)\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/index/message?groupId=${item.id}&name=${item.title}&avatar=${item.avatar}`\r\n\t\t\t})\r\n\t\t},\r\n\t\tasync loadData() {\r\n\t\t\tlet self = this\r\n\t\t\tlet data = {}\r\n\t\t\tconst [res, err] = await listUser(data)\r\n\r\n\t\t\tif (res) {\r\n\t\t\t\t//需要把用户全部存起来\r\n\r\n\t\t\t\tfor (let i = 0; i < res.length; i++) {\r\n\t\t\t\t\tlet userArr = res[i].userArr\r\n\t\t\t\t\tconsole.log('userArr', userArr)\r\n\t\t\t\t\tfor (let j = 0; j < userArr.length; j++) {\r\n\t\t\t\t\t\tlet userItem = userArr[j]\r\n\t\t\t\t\t\tself.userMap[userItem.userId] = userItem\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (res[i]['lastMsg']) {\r\n\t\t\t\t\t\tres[i]['lastMsg']['nickname'] = self.userMap[res[i]['lastMsg']['userId']]['nickname']\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//res[i][\"lastMsg\"][\"nickname\"] = userMap[res[i][\"lastMsg\"][\"userId\"]][\"nickname\"]\r\n\t\t\t\t}\r\n\t\t\t\tthis.users = res\r\n\t\t\t\tCache.set('userMap', JSON.stringify(self.userMap))\r\n\t\t\t}\r\n\t\t\tif (err) {\r\n\t\t\t\tconsole.log('🚀 ~ loadData ~ err:', err)\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync connectMqtt() {\r\n\t\t\tvar self = this\r\n\r\n\t\t\tlet userInfo = {\r\n\t\t\t\tuserId: '1921822887908581377',\r\n\t\t\t\tnickname: '范发发',\r\n\t\t\t\tchannelCode: 'hbs119',\r\n\t\t\t\tavatar: 'https://dummyimage.com/200x200/3c9cff/fff',\r\n\t\t\t\twsUrl: 'ws://mqtt.r.cdhuyun.com:8084/mqtt',\r\n\t\t\t\ttoken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NDk5MDk0Nn0.z1YlE3a8RqVz_kqIRPlkJqcyjqXWsp6zrcFOsSBUSk8'\r\n\t\t\t}\r\n\t\t\tlet opts = {\r\n\t\t\t\t// protocol: 'wxs',\r\n\t\t\t\t// port: 8084,\r\n\t\t\t\tprotocolVersion: 4,\r\n\t\t\t\tclientId: userInfo.userId,\r\n\t\t\t\tusername: userInfo.channelCode,\r\n\t\t\t\tpassword: userInfo.token,\r\n\t\t\t\tclean: false,\r\n\t\t\t\trejectUnauthorized: false\r\n\t\t\t}\r\n\t\t\tself.mqttClient = mqtt.connect(userInfo.wsUrl, opts)\r\n\t\t\tself.mqttClient\r\n\t\t\t\t.on('connect', function () {\r\n\t\t\t\t\t//self.logs.push('on connect')\r\n\t\t\t\t\tclearInterval(self.mqttPingIntetval)\r\n\t\t\t\t\tself.mqttPingIntetval = setInterval(function () {\r\n\t\t\t\t\t\tself.mqttClient.publish('/chat/server/' + userInfo.userId + '/ping', '1')\r\n\t\t\t\t\t\tconsole.log('ping')\r\n\t\t\t\t\t}, 10000)\r\n\t\t\t\t\tself.mqttClient.subscribe('/chat/client/' + userInfo.userId, function (err) {\r\n\t\t\t\t\t\tif (!err) {\r\n\t\t\t\t\t\t\tconsole.log('订阅成功')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t.on('reconnect', function () {\r\n\t\t\t\t\t//self.logs.push('on reconnect')\r\n\t\t\t\t})\r\n\t\t\t\t.on('error', function () {\r\n\t\t\t\t\tconsole.log('🚀 ~ connectMqtt ~ error:', error)\r\n\t\t\t\t\t//self.logs.push('on error')\r\n\t\t\t\t})\r\n\t\t\t\t.on('end', function () {\r\n\t\t\t\t\tconsole.log('MQTT断开连接')\r\n\t\t\t\t\t//self.logs.push('on end')\r\n\t\t\t\t})\r\n\t\t\t\t.on('message', function (topic, message) {\r\n\t\t\t\t\tlet mqttMsg = JSON.parse(message.toString())\r\n\t\t\t\t\tif (mqttMsg['command'] === 'chatMsg') {\r\n\t\t\t\t\t\tlet chatMsg = mqttMsg['data']\r\n\t\t\t\t\t\tif (self.userMap.hasOwnProperty(chatMsg['userId'])) {\r\n\t\t\t\t\t\t\tchatMsg['nickname'] = self.userMap[chatMsg['userId']]['nickname']\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tfor (let i = 0; i < self.users.length; i++) {\r\n\t\t\t\t\t\t\tif (self.users[i]['id'] === chatMsg['groupId']) {\r\n\t\t\t\t\t\t\t\tself.users[i]['lastMsg'] = chatMsg\r\n\t\t\t\t\t\t\t\tself.users[i]['updateTime'] = chatMsg.createTime\r\n\t\t\t\t\t\t\t\tself.users[i]['notReadNum'] = self.users[i]['notReadNum'] + 1\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 消息通知\r\n\t\t\t\t\t\tconsole.log('🚀 ~ chatMsg:', chatMsg)\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t//self.logs.push(message.toString())\r\n\t\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.page {\r\n\tposition: fixed;\r\n\tz-index: 1;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tbottom: 0;\r\n\tright: 0;\r\n}\r\n.navigationBar-box {\r\n\twidth: 100%;\r\n\tbackground-color: #ececec;\r\n\r\n\t.navigationBar {\r\n\t\twidth: 100%;\r\n\r\n\t\t.navigationBar-icon {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 40rpx;\r\n\t\t\tmargin-left: 30rpx;\r\n\r\n\t\t\t// 下拉\r\n\t\t\t.dropDown {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\twidth: 260rpx;\r\n\t\t\t\tmin-height: 0rpx;\r\n\t\t\t\ttop: -20rpx;\r\n\t\t\t\tright: -14rpx;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\tvisibility: 0;\r\n\t\t\t\topacity: 0;\r\n\t\t\t\ttransform: translateY(50rpx);\r\n\t\t\t\tcolor: #000;\r\n\r\n\t\t\t\t.list {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 0rpx;\r\n\t\t\t\t\tfont-size: 0rpx;\r\n\r\n\t\t\t\t\t.list-icon {\r\n\t\t\t\t\t\twidth: 0rpx;\r\n\t\t\t\t\t\theight: 0rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.dropDown::after {\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tborder: 15rpx solid #4c4c4c;\r\n\t\t\t\tborder-left-color: transparent;\r\n\t\t\t\tborder-right-color: transparent;\r\n\t\t\t\tborder-top-color: transparent;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: -28rpx;\r\n\t\t\t\tright: 26rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.active {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: 99;\r\n\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\tpadding-top: 10rpx;\r\n\t\t\t\ttransform: translateY(100rpx);\r\n\t\t\t\topacity: 1;\r\n\t\t\t\tvisibility: 1;\r\n\t\t\t\tbackground-color: #4c4c4c;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tbox-shadow: 0 0 20rpx #dcdcdc;\r\n\r\n\t\t\t\t.list {\r\n\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t\ttransition: 0.3s;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tline-height: 80rpx;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t.list-title {\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.list-icon {\r\n\t\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.navigationBar-icon2 {\r\n\t\t\twidth: 50rpx;\r\n\t\t\theight: 50rpx;\r\n\t\t\tmargin-right: 34rpx;\r\n\t\t}\r\n\r\n\t\t.navigationBar-text {\r\n\t\t\tfont-size: 16px;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.conversations {\r\n\tbox-sizing: border-box;\r\n\twidth: 750rpx;\r\n\theight: 0;\r\n\t.scroll-item-box {\r\n\t\twidth: 750rpx;\r\n\t\theight: 140rpx;\r\n\t\tmargin-bottom: 0px;\r\n\t\t.scroll-item {\r\n\t\t\tposition: relative;\r\n\t\t\theight: 100rpx;\r\n\t\t\tmargin: 25rpx 0 15rpx 0;\r\n\t\t\t.item-head {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\twidth: 90rpx;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tmargin: 0 20rpx 0 30rpx;\r\n\t\t\t\t.item-head-img {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tbackground-color: #f1f1f1;\r\n\t\t\t\t}\r\n\t\t\t\t.item-head_unread {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: -10rpx;\r\n\t\t\t\t\tright: -10rpx;\r\n\t\t\t\t\twidth: 20rpx;\r\n\t\t\t\t\theight: 20rpx;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tbackground-color: #fa5251;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.scroll-item_info {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\twidth: 590rpx;\r\n\t\t\t\t.item_info-text {\r\n\t\t\t\t\tcolor: #b6b6b6;\r\n\t\t\t\t}\r\n\t\t\t\t.m-line {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: -25rpx;\r\n\t\t\t\t\twidth: calc(100% + 80rpx);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.scroll_item_box {\r\n\t\tbackground-color: #ededed;\r\n\t}\r\n\t.hover_classr {\r\n\t\tbackground-color: #e7e7e7;\r\n\t}\r\n}\r\n.no-conversation {\r\n\twidth: 300rpx;\r\n\theight: 300rpx;\r\n\tmargin: 150rpx auto 20rpx auto;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=cd712850&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=cd712850&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755156565978\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}