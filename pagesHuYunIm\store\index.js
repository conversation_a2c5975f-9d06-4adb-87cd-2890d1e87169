import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)
const store = new Vuex.Store({
  state: {
    token:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTIzOTIyMX0.LeiLbQ2sbbFxd39ISJKqf2_1KISCwEAhGsd0J7MMBrE',
    userInfo: {
      userId: '1921822887908581377',
      nickname: '范发发',
      channelCode: 'hbs119',
      avatar: 'https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain',
      token:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTIzOTIyMX0.LeiLbQ2sbbFxd39ISJKqf2_1KISCwEAhGsd0J7MMBrE'
    }
  },
  mutations: {
    // 设置token
    SET_TOKEN(state, value) {
      state.token = value
    }
  }
})
export default store
