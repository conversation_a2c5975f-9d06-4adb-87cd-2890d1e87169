<template>
	<view class="flex_c_c page">
		<view :style="{ height: statusBar + 'px' }"></view>
		<view class="top">
			<view class="icon_ text_32 top-title">
				<view class="top-title-text" @click="to()">取消</view>
				<view class="flex1 bold_ icon_">设置字体大小</view>
				<view class="size_white icon_ text_30 top-title-button" @click="to()">完成</view>
			</view>
		</view>
		<view :id="page_font_size" class="flex1" style="width: 100%">
			<view class="flex_r row">
				<view class="flex1"></view>
				<view class="">
					<m-text :isMy="true" value="预览字体大小"></m-text>
				</view>
				<view class="row-img">
					<image class="img" src="https://pic1.zhimg.com/v2-f157b063296169e493cca2ed8f429a29_r.jpg" mode="aspectFill"></image>
				</view>
			</view>
			<view class="flex_r row">
				<view class="row-img">
					<image class="img" src="https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain" mode="aspectFill"></image>
				</view>
				<view class="">
					<m-text :isMy="false" value="拖动下面的滑块，可设置字体大小"></m-text>
				</view>
				<view class="flex1"></view>
				<view class="row-img"></view>
			</view>
			<view class="flex_r row">
				<view class="row-img">
					<image class="img" src="https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain" mode="aspectFill"></image>
				</view>
				<view class="">
					<m-text :isMy="false" value="设置后，会改变聊天中的字体大小。如果在使用过程中存在问题或意见，可反馈给仟金科技团队。"></m-text>
				</view>
				<view class="flex1"></view>
				<view class="row-img"></view>
			</view>
		</view>
		<view class="bottom-box">
			<view class="icon_ bottom">
				<view class="text_30">A</view>
				<view class="flex1 bottom-slider">
					<view class="z_index2">
						<slider activeColor="#cacaca" backgroundColor="#cacaca" :value="value" @change="sliderChange" min="1" max="4" step="1" />
					</view>
					<view class="flex_r fa_c bottom-slider-scale">
						<view class="bottom-slider-scale-str bottom-slider-scale-str1"></view>
						<view style="width: 33.33%"></view>
						<view class="bottom-slider-scale-str bottom-slider-scale-str2"></view>
						<view style="width: 33.33%"></view>
						<view class="bottom-slider-scale-str bottom-slider-scale-str3"></view>
						<view style="width: 33.33%"></view>
						<view class="bottom-slider-scale-str bottom-slider-scale-str4"></view>
					</view>
				</view>
				<view class="text_46">A</view>
			</view>
			<m-bottom-paceholder></m-bottom-paceholder>
		</view>
	</view>
</template>

<script>
import { to, show, throttle } from '@/utils/index.js';
import { mapState } from 'vuex';
import mText from './m-text.vue';
export default {
	components: {
		mText
	},
	data() {
		return {
			value: '1'
		};
	},
	onLoad() {
		let font_size = {
			page_font_size: 1,
			page_font_size_max: 2,
			page_font_size_max_plus: 3,
			page_font_size_max_plus_pro: 4
		};
		this.value = font_size[this.page_font_size];
	},
	computed: mapState({
		statusBar: (state) => state.StatusBar.statusBar,
		page_font_size: (state) => state.page_font_size
	}),
	methods: {
		to,
		sliderChange(e) {
			let font_size = {
				1: 'page_font_size',
				2: 'page_font_size_max',
				3: 'page_font_size_max_plus',
				4: 'page_font_size_max_plus_pro'
			};
			this.$store.commit('SET_page_font_size', font_size[e.detail.value] || 'page_font_size');
		}
	}
};
</script>
<style lang="scss" scoped>
.page {
	position: relative;
	width: 100%;
	height: 100vh;
	background-color: #f7f7f7;
	overflow: hidden;
	border-radius: 20rpx 20rpx 0 0;
	.top {
		width: 100%;
		margin-bottom: 20rpx;
		.top-title {
			width: calc(100% - 60rpx);
			height: 120rpx;
			margin: 0 auto;
			.top-title-text {
				width: 140rpx;
			}
			.top-title-button {
				width: 140rpx;
				height: 66rpx;
				border-radius: 10rpx;
				background-color: #4ac165;
			}
		}
	}
}
.bottom-box {
	box-sizing: border-box;
	padding: 80rpx 40rpx 40rpx 40rpx;
	width: 100%;
	background-color: #fff;
	.bottom {
		width: 100%;
	}
}

.bottom-slider {
	position: relative;
	width: 100%;
	margin: 0 20rpx;
	.bottom-slider-scale {
		z-index: 1;
		position: absolute;
		top: 0;
		left: 30rpx;
		right: 30rpx;
		bottom: 0;
		.bottom-slider-scale-str {
			position: relative;
			height: 20rpx;
			width: 2px;
			border-radius: 1px;
			background-color: #cacaca;
		}
	}
	.bottom-slider-scale-str::after {
		position: absolute;
		top: -54rpx;
		left: -35rpx;
		width: 70rpx;
		text-align: center;
		color: #999;
	}
	.bottom-slider-scale-str1::after {
		content: '标准';
	}
	.bottom-slider-scale-str2::after {
		content: '大';
	}
	.bottom-slider-scale-str3::after {
		content: '偏大';
	}
	.bottom-slider-scale-str4::after {
		content: '特大';
	}
}

.row {
	width: 100%;
	margin-bottom: 20rpx;
	.row-img {
		width: 78rpx;
		height: 78rpx;
		margin: 0 20rpx;
		flex-shrink: 0;
		border-radius: 8rpx;
		overflow: hidden;
	}
}
</style>
