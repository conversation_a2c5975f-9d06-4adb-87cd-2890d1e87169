{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_name_edit/index.vue?b6fb", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_name_edit/index.vue?1c1d", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_name_edit/index.vue?0b22", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_name_edit/index.vue?dccf", "uni-app:///pagesGoEasy/group_name_edit/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_name_edit/index.vue?e75c", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_name_edit/index.vue?1643"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "avatar", "name", "onLoad", "methods", "confirm", "uni", "sending", "groupId", "senderData", "senderId", "messageId", "payload", "timestamp", "type", "recalled", "status", "isHide"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACbA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACyB/tB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;EACA;;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBACAC;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;UACAV;QACA;QACAW;QACAC;QACAC;QACAC;QACAC;MACA;MAEAX;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/group_name_edit/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesGoEasy/group_name_edit/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=ac743ffc&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=ac743ffc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ac743ffc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/group_name_edit/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=ac743ffc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.name = \"\"\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view style=\"height: 130rpx\"></view>\r\n\t\t<view class=\"text_40 bold_ title\">修改群聊名称</view>\r\n\t\t<view class=\"text_30 text\">修改群聊名称后，将在群内通知其他成员</view>\r\n\t\t<view class=\"icon_ row\">\r\n\t\t\t<view class=\"row-img\">\r\n\t\t\t\t<image class=\"img\" :src=\"avatar\" mode=\"aspectFill\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex1 row-input\">\r\n\t\t\t\t<input v-model=\"name\" confirm-type=\"done\" focus hold-keyboard :adjust-position=\"false\" type=\"text\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"row-icon\" @click=\"name = ''\">\r\n\t\t\t\t<image\r\n\t\t\t\t\tclass=\"img\"\r\n\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMi4yIDY0LjRjLTI0Ny40IDAtNDQ3LjkgMjAwLjYtNDQ3LjkgNDQ4czIwMC41IDQ0Ny45IDQ0Ny45IDQ0Ny45IDQ0Ny45LTIwMC41IDQ0Ny45LTQ0Ny45LTIwMC41LTQ0OC00NDcuOS00NDh6bTIwMy41IDYwNS45YzEyLjUgMTIuNSAxMi41IDMzLjEgMCA0NS42cy0zMy4xIDEyLjUtNDUuNiAwbC0xNTgtMTU4LTE1OCAxNThjLTEyLjUgMTIuNS0zMy4xIDEyLjUtNDUuNiAwcy0xMi41LTMzLjEgMC00NS42bDE1OC0xNTgtMTU4LTE1OGMtMTIuNS0xMi41LTEyLjUtMzMuMSAwLTQ1LjZzMzMuMS0xMi41IDQ1LjYgMGwxNTggMTU4IDE1OC0xNThjMTIuNS0xMi41IDMzLjEtMTIuNSA0NS42IDBzMTIuNSAzMy4xIDAgNDUuNmwtMTU4IDE1OCAxNTggMTU4eiIgZmlsbD0iIzcwNzA3MCIvPjwvc3ZnPg==\"\r\n\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"icon_ text_30 bold_ size_white confirm\" @click=\"confirm\">确定</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { 自己的信息 } from '@/TEST/index';\r\nimport { show, to, jsonUrl } from '@/utils/index.js';\r\nlet group_id = null;\r\nlet group_to = {};\r\nexport default {\r\n\tcomponents: {},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tavatar: '',\r\n\t\t\tname: ''\r\n\t\t};\r\n\t},\r\n\tonLoad(e) {\r\n\t\tconst data = jsonUrl(e);\r\n\t\t// group_id = data.group_id;\r\n\t\t// group_to = data.group_to;\r\n\t\t// this.name = group_to.data.name;\r\n\t\t// this.avatar = group_to.data.avatar;\r\n\t},\r\n\tmethods: {\r\n\t\t// 提交\r\n\t\tasync confirm() {\r\n\t\t\tif (!this.name) return show('群名不能为空');\r\n\r\n\t\t\tthis.sending();\r\n\t\t\t// 更新上一个页面\r\n\t\t\tuni.$emit('getGroupInfr');\r\n\t\t\tuni.$off('getGroupInfr');\r\n\t\t\tawait show('提交成功', 1500, 'success');\r\n\t\t\tto();\r\n\t\t},\r\n\r\n\t\t// 创建自定义信息\r\n\t\tsending() {\r\n\t\t\tconst message = {\r\n\t\t\t\tgroupId: '22',\r\n\t\t\t\tsenderData: {},\r\n\t\t\t\tsenderId: 自己的信息.member_id,\r\n\t\t\t\tmessageId: Date.now(),\r\n\t\t\t\tpayload: {\r\n\t\t\t\t\tname: this.name\r\n\t\t\t\t},\r\n\t\t\t\ttimestamp: Date.now(),\r\n\t\t\t\ttype: 'update_group_name',\r\n\t\t\t\trecalled: false,\r\n\t\t\t\tstatus: 'success',\r\n\t\t\t\tisHide: 0\r\n\t\t\t};\r\n\r\n\t\t\tuni.$emit('getGroupNameMessage', message);\r\n\t\t\tuni.$off('getGroupNameMessage');\r\n\t\t},\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.title {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\ttext-align: center;\r\n\tmargin: 0 auto;\r\n}\r\n.text {\r\n\twidth: 100%;\r\n\theight: 60rpx;\r\n\ttext-align: center;\r\n\tmargin: 0 auto;\r\n}\r\n.row {\r\n\twidth: calc(100% - 60rpx);\r\n\theight: 110rpx;\r\n\tmargin: 30rpx auto 200rpx auto;\r\n\tborder-top: 0.5px solid rgba(153, 153, 153, 0.3);\r\n\tborder-bottom: 0.5px solid rgba(153, 153, 153, 0.3);\r\n\t.row-img {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\toverflow: hidden;\r\n\t\tbackground-color: #f1f1f1;\r\n\t}\r\n\t.row-input {\r\n\t\theight: 80rpx;\r\n\t\tmargin: 0 20rpx;\r\n\t\tinput {\r\n\t\t\theight: 100%;\r\n\t\t\tline-height: 80rpx;\r\n\t\t}\r\n\t}\r\n\t.row-icon {\r\n\t\twidth: 38rpx;\r\n\t\theight: 38rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n}\r\n.confirm {\r\n\twidth: 300rpx;\r\n\theight: 80rpx;\r\n\tborder-radius: 10rpx;\r\n\tmargin: 0 auto;\r\n\tbackground-color: #05c160;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=ac743ffc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=ac743ffc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755156565833\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}