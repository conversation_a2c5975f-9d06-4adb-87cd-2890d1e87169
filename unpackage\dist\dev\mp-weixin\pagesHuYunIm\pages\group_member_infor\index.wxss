@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.infor.data-v-4a000799 {
  box-sizing: border-box;
  padding: 30rpx;
  width: 100%;
}
.infor .infor-img-box.data-v-4a000799 {
  position: relative;
  margin-right: 20rpx;
}
.infor .infor-img-box .infor-img.data-v-4a000799 {
  width: 150rpx;
  height: 150rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background-color: #f1f1f1;
}
.infor .infor-img-box .avatar_pendant.data-v-4a000799 {
  position: absolute;
  z-index: 3;
  top: -2px;
  left: -2px;
  bottom: -2px;
  right: -2px;
}
.infor .infor-r .infor-r-name.data-v-4a000799 {
  width: 100%;
}
.infor .infor-r .infor-r-name .label.data-v-4a000799 {
  box-sizing: border-box;
  padding: 0 10rpx;
  line-height: 38rpx;
  margin-left: 20rpx;
  border-radius: 30rpx;
  border: 1px solid #999;
}
.action-section.data-v-4a000799 {
  margin-top: 80rpx;
  padding: 0 40rpx;
}
.action-buttons.data-v-4a000799 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 60rpx;
}
.action-btn.data-v-4a000799 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 280rpx;
  height: 120rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}
.action-btn.data-v-4a000799::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}
.action-btn.data-v-4a000799:active {
  -webkit-transform: translateY(2rpx) scale(0.98);
          transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
}
.action-btn.data-v-4a000799:active::before {
  opacity: 1;
}
.btn-content.data-v-4a000799 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}
.btn-icon.data-v-4a000799 {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.icon-text.data-v-4a000799 {
  font-size: 32rpx;
  line-height: 1;
}
.btn-text.data-v-4a000799 {
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.mute-btn.data-v-4a000799 {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-color: #dee2e6;
}
.mute-btn .btn-icon.data-v-4a000799 {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}
.mute-btn .icon-text.data-v-4a000799 {
  -webkit-filter: grayscale(1) brightness(1.2);
          filter: grayscale(1) brightness(1.2);
}
.mute-btn .btn-text.data-v-4a000799 {
  color: #6c757d;
}
.mute-btn.data-v-4a000799:active {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}
.mute-btn:active .btn-text.data-v-4a000799 {
  color: #495057;
}
.delete-btn.data-v-4a000799 {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-color: #feb2b2;
}
.delete-btn .btn-icon.data-v-4a000799 {
  background: linear-gradient(135deg, #fc8181 0%, #e53e3e 100%);
}
.delete-btn .btn-text.data-v-4a000799 {
  color: #e53e3e;
}
.delete-btn.data-v-4a000799:active {
  background: linear-gradient(135deg, #fed7d7 0%, #fc8181 100%);
}
.delete-btn:active .btn-text.data-v-4a000799 {
  color: #c53030;
}
