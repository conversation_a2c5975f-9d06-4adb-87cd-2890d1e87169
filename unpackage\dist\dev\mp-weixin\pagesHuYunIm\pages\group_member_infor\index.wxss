@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.infor.data-v-4a000799 {
  box-sizing: border-box;
  padding: 30rpx;
  width: 100%;
}
.infor .infor-img-box.data-v-4a000799 {
  position: relative;
  margin-right: 20rpx;
}
.infor .infor-img-box .infor-img.data-v-4a000799 {
  width: 150rpx;
  height: 150rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background-color: #f1f1f1;
}
.infor .infor-img-box .avatar_pendant.data-v-4a000799 {
  position: absolute;
  z-index: 3;
  top: -2px;
  left: -2px;
  bottom: -2px;
  right: -2px;
}
.infor .infor-r .infor-r-name.data-v-4a000799 {
  width: 100%;
}
.infor .infor-r .infor-r-name .label.data-v-4a000799 {
  box-sizing: border-box;
  padding: 0 10rpx;
  line-height: 38rpx;
  margin-left: 20rpx;
  border-radius: 30rpx;
  border: 1px solid #999;
}
