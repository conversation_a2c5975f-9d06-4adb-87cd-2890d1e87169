{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_member_infor/index.vue?33f6", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_member_infor/index.vue?f6dc", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_member_infor/index.vue?2fc3", "uni-app:///pagesHuYunIm/pages/group_member_infor/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_member_infor/index.vue?8648", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_member_infor/index.vue?fd69"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "senderData", "groupInfo", "onLoad", "console", "methods", "openimg", "item", "arr", "uni", "urls", "current", "handleMute", "title", "content", "confirmText", "cancelText", "success", "handleDelete", "confirmColor", "muteUser", "setTimeout", "icon", "deleteUser"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+C9uB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;UACA;UACAC;YACA;cACAC;YACA;UACA;UACAC;YACAC;YACAC;UACA;QACA;UACA;UACAF;YACAC;YACAC;UACA;QACA;MACA;QACA;QACA;QACAH;QACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAH;QACAI;QACAC;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAT;QACAI;QACAC;QACAC;QACAC;QACAG;QACAF;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAG;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAX;oBACAI;kBACA;;kBAEA;kBACA;;kBAEA;kBACAQ;oBACAZ;oBACAA;sBACAI;sBACAS;oBACA;oBACA;kBACA;gBAEA;kBACAb;kBACAA;oBACAI;oBACAS;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAd;oBACAI;kBACA;;kBAEA;kBACA;;kBAEA;kBACAQ;oBACAZ;oBACAA;sBACAI;sBACAS;oBACA;oBACA;oBACAD;sBACAZ;oBACA;kBACA;gBAEA;kBACAA;kBACAA;oBACAI;oBACAS;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzLA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/group_member_infor/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesHuYunIm/pages/group_member_infor/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4a000799&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4a000799&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4a000799\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/group_member_infor/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4a000799&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view>\n    <view style=\"height: 20rpx\"></view>\n    <view class=\"flex_r fa_c infor\">\n      <view class=\"infor-img-box\" @click=\"openimg(senderData.avatar)\">\n        <view class=\"avatar_pendant\" v-if=\"senderData.avatar\">\n          <image class=\"img\" :src=\"senderData.avatar\" mode=\"aspectFill\"></image>\n        </view>\n        <view class=\"infor-img\">\n          <image class=\"img\" src=\"\" mode=\"aspectFill\"></image>\n        </view>\n      </view>\n      <view class=\"flex1 infor-r\">\n        <view class=\"flex_r fa_c infor-r-name\">\n          <view class=\"text_34 bold_\">{{ senderData.nickname }}</view>\n          <!-- <view class=\"text_20 color__ label\">{{ pageObj.group_name }}</view> -->\n        </view>\n        <view class=\"text_26 color__\">群昵称：{{ groupInfo.title }}</view>\n        <view class=\"text_26 color__\">ID：{{ senderData.userId }}</view>\n      </view>\n    </view>\n\n    <!-- 操作按钮区域 -->\n    <view class=\"action-section\">\n      <view class=\"action-buttons\">\n        <view class=\"action-btn mute-btn\" @click=\"handleMute\">\n          <view class=\"btn-content\">\n            <view class=\"btn-icon\">\n              <text class=\"icon-text\">🔇</text>\n            </view>\n            <text class=\"btn-text\">禁言</text>\n          </view>\n        </view>\n        <view class=\"action-btn delete-btn\" @click=\"handleDelete\">\n          <view class=\"btn-content\">\n            <view class=\"btn-icon\">\n              <text class=\"icon-text\">🗑️</text>\n            </view>\n            <text class=\"btn-text\">删除</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nlet data = null\nexport default {\n  data() {\n    return {\n      senderData: {},\n      groupInfo: {}\n    }\n  },\n  onLoad(e) {\n    console.log('🚀 ~ onLoad ~ e:', e)\n    this.senderData = JSON.parse(e.memberInfo)\n    this.groupInfo = JSON.parse(e.groupInfo)\n  },\n  methods: {\n    openimg(index, item, attributes = '') {\n      if (item) {\n        // 数组对象请况\n        if (attributes) {\n          let arr = []\n          item.forEach((item, ix) => {\n            if (item[attributes]) {\n              arr.push(item[attributes])\n            }\n          })\n          uni.previewImage({\n            urls: arr,\n            current: arr[index]\n          })\n        } else {\n          // 数组请况\n          uni.previewImage({\n            urls: item,\n            current: item[index]\n          })\n        }\n      } else if (!item) {\n        //传入单张照片\n        let arr = []\n        arr.push(index)\n        uni.previewImage({\n          urls: arr,\n          current: arr[1]\n        })\n      }\n    },\n\n    // 处理禁言操作\n    handleMute() {\n      uni.showModal({\n        title: '禁言确认',\n        content: `确定要禁言 ${this.senderData.nickname} 吗？`,\n        confirmText: '确定',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            this.muteUser()\n          }\n        }\n      })\n    },\n\n    // 处理删除操作\n    handleDelete() {\n      uni.showModal({\n        title: '删除确认',\n        content: `确定要将 ${this.senderData.nickname} 移出群聊吗？`,\n        confirmText: '确定',\n        cancelText: '取消',\n        confirmColor: '#E43D33',\n        success: (res) => {\n          if (res.confirm) {\n            this.deleteUser()\n          }\n        }\n      })\n    },\n\n    // 禁言用户\n    async muteUser() {\n      try {\n        uni.showLoading({\n          title: '处理中...'\n        })\n\n        // TODO: 调用禁言API\n        // const result = await this.API_muteUser(this.senderData.userId, this.groupInfo.id)\n\n        // 模拟API调用\n        setTimeout(() => {\n          uni.hideLoading()\n          uni.showToast({\n            title: '禁言成功',\n            icon: 'success'\n          })\n          // 可以在这里更新用户状态或返回上一页\n        }, 1000)\n\n      } catch (error) {\n        uni.hideLoading()\n        uni.showToast({\n          title: '禁言失败',\n          icon: 'error'\n        })\n      }\n    },\n\n    // 删除用户\n    async deleteUser() {\n      try {\n        uni.showLoading({\n          title: '处理中...'\n        })\n\n        // TODO: 调用删除API\n        // const result = await this.API_kickUser(this.senderData.userId, this.groupInfo.id)\n\n        // 模拟API调用\n        setTimeout(() => {\n          uni.hideLoading()\n          uni.showToast({\n            title: '移除成功',\n            icon: 'success'\n          })\n          // 返回上一页\n          setTimeout(() => {\n            uni.navigateBack()\n          }, 1500)\n        }, 1000)\n\n      } catch (error) {\n        uni.hideLoading()\n        uni.showToast({\n          title: '移除失败',\n          icon: 'error'\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.infor {\n  box-sizing: border-box;\n  padding: 30rpx;\n  width: 100%;\n  .infor-img-box {\n    position: relative;\n    margin-right: 20rpx;\n    .infor-img {\n      width: 150rpx;\n      height: 150rpx;\n      border-radius: 20rpx;\n      overflow: hidden;\n      background-color: #f1f1f1;\n    }\n    .avatar_pendant {\n      position: absolute;\n      z-index: 3;\n      top: -2px;\n      left: -2px;\n      bottom: -2px;\n      right: -2px;\n    }\n  }\n\n  .infor-r {\n    .infor-r-name {\n      width: 100%;\n      .label {\n        box-sizing: border-box;\n        padding: 0 10rpx;\n        // height: 38rpx;\n        line-height: 38rpx;\n        margin-left: 20rpx;\n        border-radius: 30rpx;\n        border: 1px solid #999;\n      }\n    }\n  }\n}\n\n// 操作按钮区域样式\n.action-section {\n  margin-top: 80rpx;\n  padding: 0 40rpx;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 60rpx;\n}\n\n.action-btn {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 280rpx;\n  height: 120rpx;\n  border-radius: 24rpx;\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\n  border: 2rpx solid #f0f0f0;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);\n    opacity: 0;\n    transition: opacity 0.3s ease;\n  }\n\n  &:active {\n    transform: translateY(2rpx) scale(0.98);\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);\n\n    &::before {\n      opacity: 1;\n    }\n  }\n}\n\n.btn-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  z-index: 1;\n}\n\n.btn-icon {\n  width: 56rpx;\n  height: 56rpx;\n  margin-bottom: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  background-color: rgba(255, 255, 255, 0.9);\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.icon-text {\n  font-size: 32rpx;\n  line-height: 1;\n}\n\n.btn-text {\n  font-size: 28rpx;\n  font-weight: 600;\n  letter-spacing: 1rpx;\n}\n\n// 禁言按钮特殊样式\n.mute-btn {\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-color: #dee2e6;\n\n  .btn-icon {\n    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\n  }\n\n  .icon-text {\n    filter: grayscale(1) brightness(1.2);\n  }\n\n  .btn-text {\n    color: #6c757d;\n  }\n\n  &:active {\n    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);\n\n    .btn-text {\n      color: #495057;\n    }\n  }\n}\n\n// 删除按钮特殊样式\n.delete-btn {\n  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);\n  border-color: #feb2b2;\n\n  .btn-icon {\n    background: linear-gradient(135deg, #fc8181 0%, #e53e3e 100%);\n  }\n\n  .btn-text {\n    color: #e53e3e;\n  }\n\n  &:active {\n    background: linear-gradient(135deg, #fed7d7 0%, #fc8181 100%);\n\n    .btn-text {\n      color: #c53030;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4a000799&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4a000799&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755157660801\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}