{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_member_infor/index.vue?33f6", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_member_infor/index.vue?f6dc", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_member_infor/index.vue?2fc3", "uni-app:///pagesHuYunIm/pages/group_member_infor/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_member_infor/index.vue?8648", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_member_infor/index.vue?fd69"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "senderData", "groupInfo", "onLoad", "console", "methods", "openimg", "item", "arr", "uni", "urls", "current"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyB9uB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;UACA;UACAC;YACA;cACAC;YACA;UACA;UACAC;YACAC;YACAC;UACA;QACA;UACA;UACAF;YACAC;YACAC;UACA;QACA;MACA;QACA;QACA;QACAH;QACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/group_member_infor/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesHuYunIm/pages/group_member_infor/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4a000799&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4a000799&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4a000799\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/group_member_infor/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4a000799&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view>\n    <view style=\"height: 20rpx\"></view>\n    <view class=\"flex_r fa_c infor\">\n      <view class=\"infor-img-box\" @click=\"openimg(senderData.avatar)\">\n        <view class=\"avatar_pendant\" v-if=\"senderData.avatar\">\n          <image class=\"img\" :src=\"senderData.avatar\" mode=\"aspectFill\"></image>\n        </view>\n        <view class=\"infor-img\">\n          <image class=\"img\" src=\"\" mode=\"aspectFill\"></image>\n        </view>\n      </view>\n      <view class=\"flex1 infor-r\">\n        <view class=\"flex_r fa_c infor-r-name\">\n          <view class=\"text_34 bold_\">{{ senderData.nickname }}</view>\n          <!-- <view class=\"text_20 color__ label\">{{ pageObj.group_name }}</view> -->\n        </view>\n        <view class=\"text_26 color__\">群昵称：{{ groupInfo.title }}</view>\n        <view class=\"text_26 color__\">ID：{{ senderData.userId }}</view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nlet data = null\nexport default {\n  data() {\n    return {\n      senderData: {},\n      groupInfo: {}\n    }\n  },\n  onLoad(e) {\n    console.log('🚀 ~ onLoad ~ e:', e)\n    this.senderData = JSON.parse(e.memberInfo)\n    this.groupInfo = JSON.parse(e.groupInfo)\n  },\n  methods: {\n    openimg(index, item, attributes = '') {\n      if (item) {\n        // 数组对象请况\n        if (attributes) {\n          let arr = []\n          item.forEach((item, ix) => {\n            if (item[attributes]) {\n              arr.push(item[attributes])\n            }\n          })\n          uni.previewImage({\n            urls: arr,\n            current: arr[index]\n          })\n        } else {\n          // 数组请况\n          uni.previewImage({\n            urls: item,\n            current: item[index]\n          })\n        }\n      } else if (!item) {\n        //传入单张照片\n        let arr = []\n        arr.push(index)\n        uni.previewImage({\n          urls: arr,\n          current: arr[1]\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.infor {\n  box-sizing: border-box;\n  padding: 30rpx;\n  width: 100%;\n  .infor-img-box {\n    position: relative;\n    margin-right: 20rpx;\n    .infor-img {\n      width: 150rpx;\n      height: 150rpx;\n      border-radius: 20rpx;\n      overflow: hidden;\n      background-color: #f1f1f1;\n    }\n    .avatar_pendant {\n      position: absolute;\n      z-index: 3;\n      top: -2px;\n      left: -2px;\n      bottom: -2px;\n      right: -2px;\n    }\n  }\n\n  .infor-r {\n    .infor-r-name {\n      width: 100%;\n      .label {\n        box-sizing: border-box;\n        padding: 0 10rpx;\n        // height: 38rpx;\n        line-height: 38rpx;\n        margin-left: 20rpx;\n        border-radius: 30rpx;\n        border: 1px solid #999;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4a000799&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4a000799&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755157182646\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}