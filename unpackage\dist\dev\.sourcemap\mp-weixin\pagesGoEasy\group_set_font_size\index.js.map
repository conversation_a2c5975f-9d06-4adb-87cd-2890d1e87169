{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_set_font_size/index.vue?0939", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_set_font_size/index.vue?e247", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_set_font_size/index.vue?ebd4", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_set_font_size/index.vue?ab56", "uni-app:///pagesGoEasy/group_set_font_size/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_set_font_size/index.vue?81da", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/group_set_font_size/index.vue?515c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "mText", "data", "value", "onLoad", "page_font_size", "page_font_size_max", "page_font_size_max_plus", "page_font_size_max_plus_pro", "computed", "statusBar", "methods", "to", "slider<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACkE/tB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAL;MAAA;IAAA;EACA;EACAM;IACAC;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/group_set_font_size/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesGoEasy/group_set_font_size/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=9f8d81d0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=9f8d81d0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9f8d81d0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/group_set_font_size/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=9f8d81d0&scoped=true&\"", "var components\ntry {\n  components = {\n    mBottomPaceholder: function () {\n      return import(\n        /* webpackChunkName: \"components/m-bottom-paceholder/m-bottom-paceholder\" */ \"@/components/m-bottom-paceholder/m-bottom-paceholder.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"flex_c_c page\">\n\t\t<view :style=\"{ height: statusBar + 'px' }\"></view>\n\t\t<view class=\"top\">\n\t\t\t<view class=\"icon_ text_32 top-title\">\n\t\t\t\t<view class=\"top-title-text\" @click=\"to()\">取消</view>\n\t\t\t\t<view class=\"flex1 bold_ icon_\">设置字体大小</view>\n\t\t\t\t<view class=\"size_white icon_ text_30 top-title-button\" @click=\"to()\">完成</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view :id=\"page_font_size\" class=\"flex1\" style=\"width: 100%\">\n\t\t\t<view class=\"flex_r row\">\n\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t<view class=\"\">\n\t\t\t\t\t<m-text :isMy=\"true\" value=\"预览字体大小\"></m-text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"row-img\">\n\t\t\t\t\t<image class=\"img\" src=\"https://pic1.zhimg.com/v2-f157b063296169e493cca2ed8f429a29_r.jpg\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex_r row\">\n\t\t\t\t<view class=\"row-img\">\n\t\t\t\t\t<image class=\"img\" src=\"https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"\">\n\t\t\t\t\t<m-text :isMy=\"false\" value=\"拖动下面的滑块，可设置字体大小\"></m-text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t<view class=\"row-img\"></view>\n\t\t\t</view>\n\t\t\t<view class=\"flex_r row\">\n\t\t\t\t<view class=\"row-img\">\n\t\t\t\t\t<image class=\"img\" src=\"https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"\">\n\t\t\t\t\t<m-text :isMy=\"false\" value=\"设置后，会改变聊天中的字体大小。如果在使用过程中存在问题或意见，可反馈给仟金科技团队。\"></m-text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t<view class=\"row-img\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"bottom-box\">\n\t\t\t<view class=\"icon_ bottom\">\n\t\t\t\t<view class=\"text_30\">A</view>\n\t\t\t\t<view class=\"flex1 bottom-slider\">\n\t\t\t\t\t<view class=\"z_index2\">\n\t\t\t\t\t\t<slider activeColor=\"#cacaca\" backgroundColor=\"#cacaca\" :value=\"value\" @change=\"sliderChange\" min=\"1\" max=\"4\" step=\"1\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex_r fa_c bottom-slider-scale\">\n\t\t\t\t\t\t<view class=\"bottom-slider-scale-str bottom-slider-scale-str1\"></view>\n\t\t\t\t\t\t<view style=\"width: 33.33%\"></view>\n\t\t\t\t\t\t<view class=\"bottom-slider-scale-str bottom-slider-scale-str2\"></view>\n\t\t\t\t\t\t<view style=\"width: 33.33%\"></view>\n\t\t\t\t\t\t<view class=\"bottom-slider-scale-str bottom-slider-scale-str3\"></view>\n\t\t\t\t\t\t<view style=\"width: 33.33%\"></view>\n\t\t\t\t\t\t<view class=\"bottom-slider-scale-str bottom-slider-scale-str4\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"text_46\">A</view>\n\t\t\t</view>\n\t\t\t<m-bottom-paceholder></m-bottom-paceholder>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { to, show, throttle } from '@/utils/index.js';\nimport { mapState } from 'vuex';\nimport mText from './m-text.vue';\nexport default {\n\tcomponents: {\n\t\tmText\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tvalue: '1'\n\t\t};\n\t},\n\tonLoad() {\n\t\tlet font_size = {\n\t\t\tpage_font_size: 1,\n\t\t\tpage_font_size_max: 2,\n\t\t\tpage_font_size_max_plus: 3,\n\t\t\tpage_font_size_max_plus_pro: 4\n\t\t};\n\t\tthis.value = font_size[this.page_font_size];\n\t},\n\tcomputed: mapState({\n\t\tstatusBar: (state) => state.StatusBar.statusBar,\n\t\tpage_font_size: (state) => state.page_font_size\n\t}),\n\tmethods: {\n\t\tto,\n\t\tsliderChange(e) {\n\t\t\tlet font_size = {\n\t\t\t\t1: 'page_font_size',\n\t\t\t\t2: 'page_font_size_max',\n\t\t\t\t3: 'page_font_size_max_plus',\n\t\t\t\t4: 'page_font_size_max_plus_pro'\n\t\t\t};\n\t\t\tthis.$store.commit('SET_page_font_size', font_size[e.detail.value] || 'page_font_size');\n\t\t}\n\t}\n};\n</script>\n<style lang=\"scss\" scoped>\n.page {\n\tposition: relative;\n\twidth: 100%;\n\theight: 100vh;\n\tbackground-color: #f7f7f7;\n\toverflow: hidden;\n\tborder-radius: 20rpx 20rpx 0 0;\n\t.top {\n\t\twidth: 100%;\r\n\t\tmargin-bottom: 20rpx;\n\t\t.top-title {\n\t\t\twidth: calc(100% - 60rpx);\n\t\t\theight: 120rpx;\n\t\t\tmargin: 0 auto;\n\t\t\t.top-title-text {\n\t\t\t\twidth: 140rpx;\n\t\t\t}\n\t\t\t.top-title-button {\n\t\t\t\twidth: 140rpx;\n\t\t\t\theight: 66rpx;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tbackground-color: #4ac165;\n\t\t\t}\n\t\t}\n\t}\n}\n.bottom-box {\n\tbox-sizing: border-box;\n\tpadding: 80rpx 40rpx 40rpx 40rpx;\n\twidth: 100%;\n\tbackground-color: #fff;\n\t.bottom {\n\t\twidth: 100%;\n\t}\n}\n\n.bottom-slider {\n\tposition: relative;\n\twidth: 100%;\n\tmargin: 0 20rpx;\n\t.bottom-slider-scale {\n\t\tz-index: 1;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 30rpx;\n\t\tright: 30rpx;\n\t\tbottom: 0;\n\t\t.bottom-slider-scale-str {\n\t\t\tposition: relative;\n\t\t\theight: 20rpx;\n\t\t\twidth: 2px;\n\t\t\tborder-radius: 1px;\n\t\t\tbackground-color: #cacaca;\n\t\t}\n\t}\n\t.bottom-slider-scale-str::after {\n\t\tposition: absolute;\n\t\ttop: -54rpx;\n\t\tleft: -35rpx;\n\t\twidth: 70rpx;\n\t\ttext-align: center;\n\t\tcolor: #999;\n\t}\n\t.bottom-slider-scale-str1::after {\n\t\tcontent: '标准';\n\t}\n\t.bottom-slider-scale-str2::after {\n\t\tcontent: '大';\n\t}\n\t.bottom-slider-scale-str3::after {\n\t\tcontent: '偏大';\n\t}\n\t.bottom-slider-scale-str4::after {\n\t\tcontent: '特大';\n\t}\n}\n\n.row {\n\twidth: 100%;\n\tmargin-bottom: 20rpx;\n\t.row-img {\n\t\twidth: 78rpx;\n\t\theight: 78rpx;\n\t\tmargin: 0 20rpx;\n\t\tflex-shrink: 0;\n\t\tborder-radius: 8rpx;\n\t\toverflow: hidden;\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=9f8d81d0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=9f8d81d0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755156565879\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}