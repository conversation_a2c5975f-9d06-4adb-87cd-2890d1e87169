@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-023374b0 {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #f7f7f7;
  overflow: hidden;
  border-radius: 20rpx 20rpx 0 0;
}
.page .top.data-v-023374b0 {
  width: 100%;
  margin-bottom: 20rpx;
}
.page .top .top-title.data-v-023374b0 {
  width: calc(100% - 60rpx);
  height: 120rpx;
  margin: 0 auto;
}
.page .top .top-title .top-title-text.data-v-023374b0 {
  width: 140rpx;
}
.page .top .top-title .top-title-button.data-v-023374b0 {
  width: 140rpx;
  height: 66rpx;
  border-radius: 10rpx;
  background-color: #4ac165;
}
.bottom-box.data-v-023374b0 {
  box-sizing: border-box;
  padding: 80rpx 40rpx 40rpx 40rpx;
  width: 100%;
  background-color: #fff;
}
.bottom-box .bottom.data-v-023374b0 {
  width: 100%;
}
.bottom-slider.data-v-023374b0 {
  position: relative;
  width: 100%;
  margin: 0 20rpx;
}
.bottom-slider .bottom-slider-scale.data-v-023374b0 {
  z-index: 1;
  position: absolute;
  top: 0;
  left: 30rpx;
  right: 30rpx;
  bottom: 0;
}
.bottom-slider .bottom-slider-scale .bottom-slider-scale-str.data-v-023374b0 {
  position: relative;
  height: 20rpx;
  width: 2px;
  border-radius: 1px;
  background-color: #cacaca;
}
.bottom-slider .bottom-slider-scale-str.data-v-023374b0::after {
  position: absolute;
  top: -54rpx;
  left: -35rpx;
  width: 70rpx;
  text-align: center;
  color: #999;
}
.bottom-slider .bottom-slider-scale-str1.data-v-023374b0::after {
  content: "标准";
}
.bottom-slider .bottom-slider-scale-str2.data-v-023374b0::after {
  content: "大";
}
.bottom-slider .bottom-slider-scale-str3.data-v-023374b0::after {
  content: "偏大";
}
.bottom-slider .bottom-slider-scale-str4.data-v-023374b0::after {
  content: "特大";
}
.row.data-v-023374b0 {
  width: 100%;
  margin-bottom: 20rpx;
}
.row .row-img.data-v-023374b0 {
  width: 78rpx;
  height: 78rpx;
  margin: 0 20rpx;
  flex-shrink: 0;
  border-radius: 8rpx;
  overflow: hidden;
}
