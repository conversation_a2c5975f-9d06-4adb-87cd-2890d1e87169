{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_infor/index.vue?b5c9", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_infor/index.vue?fdbb", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_infor/index.vue?79f3", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_infor/index.vue?9267", "uni-app:///pagesHuYunIm/pages/group_infor/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_infor/index.vue?fac1", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/group_infor/index.vue?a915"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "memberSelectionLoading", "data", "<PERSON><PERSON><PERSON>", "group_id", "alllist", "list", "count", "group_to", "noticeContent", "allow_modify_nickname", "invite_status", "userInfo", "groupInfo", "onLoad", "console", "showAll", "computed", "page_font_size", "renderTextMessage", "methods", "to", "uni", "url", "toAnnouncemen", "text", "toGroupName", "toNickname", "getNotice", "getGroupMemberInfo", "getGroupInfr", "res", "id", "type", "name", "avatar", "getData", "title", "remove_member", "itemclickAll", "member_id", "itemclick", "content", "success", "API_groupMember", "API_kick", "API_notice", "API_groupMemberInfo", "API_info"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC4L9uB;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACAC;IACAC;IACAZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;;EACAa;IACAC;MAAA;IAAA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACAC;MACAC;QAAAC;MAAA;IACA;IACAP;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAQ;MACA;MACAF;MACAA;MACAD;QAAAjB;QAAAqB;QAAAjB;MAAA;IACA;IACA;IACAkB;MAAA;MACA;MACAJ;MACAA;MACAD;QAAAjB;QAAAI;MAAA;IACA;IACA;IACAmB;MACAL;MACAA;MACAD;QAAAjB;MAAA;IACA;IACA;IACAwB;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAAA;IACA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAAA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACA7B;kBACA;kBACA;kBACA;oBACA8B;oBACAC;oBACA/B;sBACAgC;sBACAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAd;kBACAe;gBACA;gBACA9B;gBACAe;kBACAe;gBACA;gBACA;gBACA;gBACA;kBACA;gBACA;gBACAf;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAgB;MACA;IACA;IACAC;MACAlB;QAAAmB;QAAApC;MAAA;IACA;IACAqC;MAAA;MACAnB;QACAoB;QACAC;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBACA;sBACA;wBACA;sBACA;oBACA,sBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IACA;IACAC;MAAA;MAAA;IAAA;IACA;IACAC;IACA;IACAC;IACA;IACAC;IACA;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5UA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/group_infor/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesHuYunIm/pages/group_infor/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=53459e40&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=53459e40&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"53459e40\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/group_infor/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=53459e40&scoped=true&\"", "var components\ntry {\n  components = {\n    mLine: function () {\n      return import(\n        /* webpackChunkName: \"components/m-line/m-line\" */ \"@/components/m-line/m-line.vue\"\n      )\n    },\n    mBottomPaceholder: function () {\n      return import(\n        /* webpackChunkName: \"components/m-bottom-paceholder/m-bottom-paceholder\" */ \"@/components/m-bottom-paceholder/m-bottom-paceholder.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view :id=\"page_font_size\">\n    <view style=\"height: 30rpx\"></view>\n    <view class=\"flex_r list\">\n      <view class=\"flex_c_c nowrap_ item\" v-for=\"(item, index) in groupInfo.userArr\" :key=\"item.userId\">\n        <view class=\"item-img\">\n          <image class=\"img\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\n        </view>\n        <view class=\"text_24 icon_ nowrap_ item-title\">\n          <text class=\"nowrap_\">{{ item.nickname }}</text>\n        </view>\n      </view>\n      <template v-if=\"invite_status\">\n        <view class=\"flex_c_c item\" @click=\"remove_member\">\n          <view class=\"item-img icon_ item_img\">\n            <image\n              class=\"img\"\n              src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTEyOCA0ODBoNzY4YzE3LjY4IDAgMzIgMTQuMzM2IDMyIDMyIDAgMTcuNjgtMTQuMzIgMzItMzIgMzJIMTI4Yy0xNy42OCAwLTMyLTE0LjMyLTMyLTMyIDAtMTcuNjY0IDE0LjMyLTMyIDMyLTMyeiIgZmlsbD0iI2IyYjJiMiIvPjwvc3ZnPg==\"\n              mode=\"aspectFill\"\n            ></image>\n          </view>\n          <view class=\"text_28 item-title\" style=\"height: 30rpx\"></view>\n        </view>\n        <view class=\"flex_c_c item\" @click=\"to(`/pagesGoEasy/admin/add_member?group_id=${group_id}`)\">\n          <view class=\"item-img icon_ item_img\">\n            <image\n              class=\"img\"\n              src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTkyOCA1NDRINTQ0djM4NGMwIDE3LjY4LTE0LjMyIDMyLTMyIDMycy0zMi0xNC4zMi0zMi0zMlY1NDRIOTZjLTE3LjY4IDAtMzItMTQuMzItMzItMzJzMTQuMzItMzIgMzItMzJoMzg0Vjk2YzAtMTcuNjggMTQuMzItMzIgMzItMzJzMzIgMTQuMzIgMzIgMzJ2Mzg0aDM4NGMxNy42OCAwIDMyIDE0LjMyIDMyIDMycy0xNC4zMiAzMi0zMiAzMnoiIGZpbGw9IiNiMmIyYjIiLz48L3N2Zz4=\"\n              mode=\"aspectFill\"\n            ></image>\n          </view>\n          <view class=\"text_28 item-title\" style=\"height: 30rpx\"></view>\n        </view>\n      </template>\n    </view>\n    <view class=\"icon_ text_26 color__ showAll\" @click=\"showAll\" v-if=\"tooMuch\">\n      <text>查看更多群成员</text>\n      <view class=\"icon_ showAll-icon\">\n        <image\n          class=\"img\"\n          src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg==\"\n          mode=\"aspectFill\"\n        ></image>\n      </view>\n    </view>\n\n    <view class=\"interval\"></view>\n\n    <!-- 群聊名称 -->\n    <view class=\"list-option\">\n      <view class=\"flex_r fa_c item\">\n        <view class=\"text_30 bold_ item-title\">群聊名称</view>\n        <view class=\"flex1\"></view>\n        <view class=\"text_30 color__ item-subtitle\">{{ groupInfo.title }}</view>\n        <view class=\"icon_ item-enter\">\n          <image\n            class=\"img\"\n            src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg==\"\n            mode=\"aspectFill\"\n          ></image>\n        </view>\n      </view>\n      <m-line color=\"#dedede\" length=\"100%\" :hairline=\"true\"></m-line>\n    </view>\n    <view class=\"list-option\">\n      <view class=\"flex_r fa_c item\" @click=\"toNickname('/pagesGoEasy/group_nickname_edit/index')\">\n        <view class=\"text_30 bold_ item-title\">我在本群的昵称</view>\n        <view class=\"flex1\"></view>\n        <view class=\"text_30 color__ item-subtitle\">{{ userInfo.nickname }}</view>\n        <view class=\"icon_ item-enter\">\n          <image\n            class=\"img\"\n            src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg==\"\n            mode=\"aspectFill\"\n          ></image>\n        </view>\n      </view>\n      <m-line color=\"#dedede\" length=\"100%\" :hairline=\"true\"></m-line>\n    </view>\n    <!-- 群二维码 -->\n    <!-- <view class=\"list-option\">\n      <view class=\"flex_r fa_c item\" @click=\"to(`/pagesGoEasy/group_code/index?group_id=${group_id}`)\">\n        <view class=\"text_30 bold_ item-title\">群二维码</view>\n        <view class=\"flex1\"></view>\n        <view class=\"item-subtitle\">\n          <image\n            class=\"img\"\n            src=\"data:image/svg+xml;base64,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\"\n            mode=\"aspectFill\"\n          ></image>\n        </view>\n        <view class=\"icon_ item-enter\">\n          <image\n            class=\"img\"\n            src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg==\"\n            mode=\"aspectFill\"\n          ></image>\n        </view>\n      </view>\n      <m-line color=\"#dedede\" length=\"100%\" :hairline=\"true\"></m-line>\n    </view> -->\n    <!-- <view class=\"interval\"></view>\n    <view class=\"list-option\">\n      <view class=\"flex_r fa_c item\" @click=\"to(`/pagesGoEasy/group_history_get/index?group_id=${group_id}`)\">\n        <view class=\"text_30 bold_ item-title\">查找记录</view>\n        <view class=\"flex1\"></view>\n        <view class=\"icon_ item-enter\">\n          <image\n            class=\"img\"\n            src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg==\"\n            mode=\"aspectFill\"\n          ></image>\n        </view>\n      </view>\n      <m-line color=\"#dedede\" length=\"100%\" :hairline=\"true\"></m-line>\n    </view> -->\n\n    <!-- <view class=\"list-option\">\n      <view class=\"flex_r fa_c item\" @click=\"to(`/pagesGoEasy/group_report/index?group_id=${group_id}`)\">\n        <view class=\"text_30 bold_ item-title\">投诉/举报</view>\n        <view class=\"flex1\"></view>\n        <view class=\"icon_ item-enter\">\n          <image\n            class=\"img\"\n            src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg==\"\n            mode=\"aspectFill\"\n          ></image>\n        </view>\n      </view>\n      <m-line color=\"#dedede\" length=\"100%\" :hairline=\"true\"></m-line>\n    </view> -->\n    <view class=\"list-option\">\n      <view class=\"flex_r fa_c item\" @click=\"to(`/pagesHuYunIm/pages/group_set_font_size/index?group_id=${group_id}`)\">\n        <view class=\"text_30 bold_ item-title\">设置聊天区字体大小</view>\n        <view class=\"flex1\"></view>\n        <view class=\"icon_ item-enter\">\n          <image\n            class=\"img\"\n            src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg==\"\n            mode=\"aspectFill\"\n          ></image>\n        </view>\n      </view>\n      <m-line color=\"#dedede\" length=\"100%\" :hairline=\"true\"></m-line>\n    </view>\n\n    <view class=\"interval\"></view>\n\n    <!-- 群公告 -->\n    <!-- <view class=\"list-option\" @click=\"toAnnouncemen\">\n      <view class=\"flex_r fa_c item\">\n        <view class=\"text_30 bold_ item-title\">群公告</view>\n        <view class=\"flex1\"></view>\n        <view class=\"icon_ item-enter\" v-if=\"invite_status\">\n          <image\n            class=\"img\"\n            src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg==\"\n            mode=\"aspectFill\"\n          ></image>\n        </view>\n      </view>\n      <view class=\"color__ text_26 list-option-text\">\n        <view class=\"text_30\" :style=\"{ whiteSpace: 'pre-wrap' }\" v-html=\"renderTextMessage\"></view>\n      </view>\n      <m-line color=\"#dedede\" length=\"100%\" :hairline=\"true\"></m-line>\n    </view> -->\n\n    <m-bottom-paceholder></m-bottom-paceholder>\n    <!-- 看全部用户 -->\n    <member-selection-loading\n      :title=\"`全部成员(${count})`\"\n      ref=\"memberSelectionRefArr\"\n      :group_id=\"group_id\"\n      :userList=\"groupInfo.userArr\"\n      @itemclick=\"itemclickAll\"\n    ></member-selection-loading>\n    <!-- 移除用户 -->\n    <member-selection-loading\n      title=\"移除群成员\"\n      ref=\"memberSelectionLoadingRef\"\n      :group_id=\"group_id\"\n      @itemclick=\"itemclick\"\n    ></member-selection-loading>\n  </view>\n</template>\n\n<script>\nimport memberSelectionLoading from '../../components/memberSelectionLoading/index'\nimport { EmojiDecoder, emojiMap } from '../../lib/EmojiDecoder.js'\nimport { mapState } from 'vuex'\nconst emojiUrl = 'https://imgcache.qq.com/open/qcloud/tim/assets/emoji/'\nconst decoder = new EmojiDecoder(emojiUrl, emojiMap)\nlet group_id = null\nlet showAll = false\nexport default {\n  components: {\n    memberSelectionLoading\n  },\n  data() {\n    return {\n      tooMuch: false, //人数是否超过25\n      group_id: null,\n      alllist: [],\n      list: [],\n      count: '--',\n      group_to: {},\n      noticeContent: '',\n      allow_modify_nickname: 0,\n      invite_status: null, //是否可拉人或踢人\n      userInfo: {}, //用户信息\n      groupInfo: {} //群信息\n    }\n  },\n  onLoad(e) {\n    this.userInfo = JSON.parse(decodeURIComponent(e.userInfo))\n    this.groupInfo = JSON.parse(decodeURIComponent(e.groupInfo))\n    console.log('🚀 ~ onLoad ~ e:', e)\n    showAll = false\n    group_id = `${e.group_id}`\n    this.group_id = group_id\n    // 获取群成员\n    this.getData()\n    // // 群信息\n    // this.getGroupInfr()\n    // // 群公告\n    // this.getNotice()\n    // // 群个性化内容\n    // this.getGroupMemberInfo()\n  },\n  computed: mapState({\n    page_font_size: (state) => state.page_font_size,\n    //渲染文本消息，如果包含表情，替换为图片\n    //todo:本不需要该方法，可以在标签里完成，但小程序有兼容性问题，被迫这样实现\n    renderTextMessage() {\n      if (!this.noticeContent) return '暂无群公告'\n      return '<span>' + decoder.decode(this.noticeContent) + '</span>'\n    }\n  }),\n  methods: {\n    to(url) {\n      uni.navigateTo({ url })\n    },\n    async showAll() {\n      this.$refs.memberSelectionRefArr.open()\n    },\n    // 修改公告\n    toAnnouncemen() {\n      if (!this.invite_status) return\n      uni.$off('getNotice', this.getNotice)\n      uni.$on('getNotice', this.getNotice)\n      to(`/pagesGoEasy/group_announcement_add/index`, { group_id, text: this.noticeContent, group_to: this.group_to })\n    },\n    // 修改群名\n    toGroupName(url, invite_status = true) {\n      if (!invite_status) return\n      uni.$off('getGroupInfr', this.getGroupInfr)\n      uni.$on('getGroupInfr', this.getGroupInfr)\n      to(`${url}`, { group_id, group_to: this.group_to })\n    },\n    // 群昵称\n    toNickname(url) {\n      uni.$off('getGroupMemberInfo', this.getGroupMemberInfo)\n      uni.$on('getGroupMemberInfo', this.getGroupMemberInfo)\n      to(`${url}`, { group_id, ...this.memberInfo })\n    },\n    // 获取群公告\n    async getNotice() {},\n    // 获取个性化\n    async getGroupMemberInfo() {},\n\n    // 获取群名等信息\n    async getGroupInfr() {\n      const res = await this.API_info()\n      if (res) {\n        const data = res.data\n        this.allow_modify_nickname = data.allow_modify_nickname\n        // console.log(allow_modify_nickname)\n        this.group_to = {\n          id: data.id,\n          type: this.GoEasy.IM_SCENE.GROUP,\n          data: {\n            name: data.name,\n            avatar: data.avatar\n          }\n        }\n      }\n    },\n    async getData() {\n      uni.showLoading({\n        title: '加载中...'\n      })\n      const count = this.groupInfo.userArr.length\n      uni.setNavigationBarTitle({\n        title: `聊天信息(${count})`\n      })\n      this.count = count\n      this.invite_status = this.groupInfo.myRoleCode === '管理员'\n      if (count.length > 25) {\n        this.tooMuch = true\n      }\n      uni.hideLoading()\n    },\n    remove_member() {\n      this.$refs.memberSelectionLoadingRef.open()\n    },\n    itemclickAll(item) {\n      to('/pagesGoEasy/group_member_infor/index', { member_id: item.member_id, group_id })\n    },\n    itemclick(item) {\n      uni.showModal({\n        content: `确定将 ${item.name} 移出群聊？`,\n        success: async (e) => {\n          if (e.confirm) {\n            this.list = this.list.filter((im) => {\n              return item.member_id != im.member_id\n            })\n          } else if (e.cancel) {\n          }\n        }\n      })\n    },\n    // 获取群成员\n    API_groupMember(page = 1, limit = 30) {},\n    // 踢出群\n    API_kick(member_id) {},\n    // 获取公告\n    API_notice() {},\n    // 获取个性化内容\n    API_groupMemberInfo() {},\n    // 获取群消息\n    API_info() {}\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.interval {\n  width: 100%;\n  height: 20rpx;\n  background-color: #ededed;\n}\n.list {\n  width: calc(100% - 40rpx);\n  flex-wrap: wrap;\n  margin: 0 auto 0 10rpx;\n  .item {\n    width: calc(20% - 20rpx);\n    margin-left: 20rpx;\n    margin-bottom: 30rpx;\n    .item-img {\n      box-sizing: border-box;\n      width: 100rpx;\n      height: 100rpx;\n      border-radius: 10rpx;\n      overflow: hidden;\n      border: 1px solid #efefef;\n    }\n    .item-title {\n      width: 100%;\n      color: #7f7f7f;\n      margin-top: 4rpx;\n    }\n    .item_img {\n      box-sizing: border-box;\n      background-color: #fff;\n      border-radius: 12rpx;\n      border: 2px dashed #cacaca;\n      .img {\n        width: 50%;\n        height: 50%;\n      }\n    }\n  }\n}\n.showAll {\n  width: 100%;\n  height: 70rpx;\n  margin-bottom: 20rpx;\n  .showAll-icon {\n    width: 34rpx;\n    height: 34rpx;\n  }\n}\n\n.list-option {\n  box-sizing: border-box;\n  width: 100%;\n  padding: 0 0 0 30rpx;\n  .item {\n    box-sizing: border-box;\n    padding-right: 20rpx;\n    width: 100%;\n    height: 100rpx;\n    .item-title {\n    }\n    .item-subtitle {\n      height: 40rpx;\n      line-height: 40rpx;\n      .img {\n        width: 40rpx;\n        height: 40rpx;\n      }\n    }\n    .item-enter {\n      width: 34rpx;\n      height: 34rpx;\n      margin-top: 4rpx;\n      margin-left: 10rpx;\n    }\n  }\n\n  .list-option-text {\n    position: relative;\n    top: -20rpx;\n    width: 100%;\n    box-sizing: border-box;\n    padding-right: 20rpx;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=53459e40&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=53459e40&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755156565865\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}