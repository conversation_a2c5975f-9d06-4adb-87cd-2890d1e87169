<template>
	<view class="flex_c row">
		<view class="flex_r text-box" :class="{ text_box: isMy }">
			<view
				class="text_32 text"
				:class="isMy ? 'text_r' : 'text_l'"
				:hover-class="isMy ? 'hover_classr' : 'hover_classl'"
				:hover-stay-time="60"
				:style="{ whiteSpace: 'pre-wrap' }"
			>
				{{ value }}
			</view>
		</view>
	</view>
</template>

<script>
export default {
	components: {},
	props: {
		isMy: {
			type: [<PERSON><PERSON><PERSON>, Number],
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {};
	},
	methods: {
		onClick() {
			this.$emit('onClick');
		}
	}
};
</script>

<style scoped lang="scss">
.row {
}
.row_ {
	flex-direction: row-reverse;
}
.text_box {
	flex-direction: row-reverse;
}
.text {
	position: relative;
	z-index: 99;
	box-sizing: border-box;
	padding: 16rpx 26rpx;
	border-radius: 8rpx;
	background-color: #fff;

	word-break: break-all;
	vertical-align: center;
}

.text_r {
	position: relative;
	background-color: #95ec6a;
}
.text_l {
	position: relative;
}

.text_r::after {
	position: absolute;
	z-index: -1;
	content: '';
	top: 26rpx;
	right: -8rpx;
	width: 18rpx;
	height: 18rpx;
	border-radius: 2px;
	transform: rotate(45deg);
	background-color: #95ec6a;
}
.text_l::after {
	position: absolute;
	z-index: -1;
	content: '';
	top: 26rpx;
	left: -8rpx;
	width: 18rpx;
	height: 18rpx;
	border-radius: 2px;
	transform: rotate(45deg);
	background-color: #fff;
}

.hover_classr {
	background-color: #89d961;
}
.hover_classl {
	background-color: #e2e2e2;
}

.hover_classr::after {
	position: absolute;
	z-index: -1;
	content: '';
	top: 26rpx;
	right: -8rpx;
	width: 18rpx;
	height: 18rpx;
	border-radius: 2px;
	transform: rotate(45deg);
	background-color: #89d961;
}
.hover_classl::after {
	position: absolute;
	z-index: -1;
	content: '';
	top: 26rpx;
	left: -8rpx;
	width: 18rpx;
	height: 18rpx;
	border-radius: 2px;
	transform: rotate(45deg);
	background-color: #e2e2e2;
}
</style>
