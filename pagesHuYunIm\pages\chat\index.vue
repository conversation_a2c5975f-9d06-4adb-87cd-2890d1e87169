<template>
  <view :id="page_font_size">
    <view class="flex_c page" @touchmove="touchmove">
      <view class="navigationRef">
        <navigation
          ref="navigationRef"
          :groupCount="groupCount"
          :title="groupInfo.title"
          :groupInfo="groupInfo"
          :group_id="pagueObj.id"
          @onMore="onMore"
        ></navigation>
      </view>
      <view class="scroll-view-str" :style="{ height: `${reserveHeight}px` }" v-if="reserveHeight > 0"></view>
      <scroll-view
        class="flex1 scroll-Y scroll-view"
        @tap.stop="onPage"
        id="scroll-view"
        lower-threshold="100"
        scroll-y
        scroll-with-animation
        :scroll-top="scroll_top"
        @scroll="scroll"
        @scrolltoupper="scrolltoupper"
        @scrolltolower="scrolltolower"
      >
        <view class="messageList_">
          <block v-for="(item, index) in list" :key="item.id">
            <!-- #ifdef APP || H5 -->
            <view class="z_index2" :class="`oneheight_${index}`" :key="item.id + index" v-if="!item.isHide">
              <view class="icon_ text_26 color__ time">
                {{ renderMessageDate(item, index) }}
              </view>
              <view :key="item.messageId + index" v-if="!item.recalled">
                <item
                  :isMy="isSelf(item.senderId)"
                  :myid="myid"
                  :item="item"
                  @onClick="onItem"
                  @onLongpress="onLongpress"
                  @mention="mention"
                  @imgLoad="imgLoad"
                ></item>
              </view>
              <view class="icon_ text_26 recalled" v-else>
                <view class="">
                  <text v-if="isSelf(item.senderId)">你</text>
                  <text v-else>{{ item.senderData.name }}</text>
                  撤回了一条消息
                </view>
                <view class="recalled-edit" v-if="item.type === 'text' && isSelf(item.senderId)" @click="recalledEdit(item)">重新编辑</view>
              </view>
            </view>
            <!-- #endif -->
            <!-- #ifdef MP -->
            <view class="z_index2">
              <view class="icon_ text_26 color__ time">
                {{ renderMessageDate(item, index) }}
              </view>
              <view :key="item.id" v-if="!item.recalled && item.msgType !== 'withdraw'">
                <item
                  :isMy="isSelf(item.userId)"
                  :myid="myid"
                  :item="item"
                  @onClick="onItem"
                  @onLongpress="onLongpress"
                  @mention="mention"
                  @onItem="handleItem"
                ></item>
              </view>
              <view class="icon_ text_26 recalled" v-else-if="item.recalled && item.msgType === 'withdraw'">
                <view class="">
                  <text>{{ getByIdUser(item.userId) }}</text>
                  撤回了一条消息
                </view>
              </view>
              <view class="icon_ text_26 recalled" v-else>
                <view class="">
                  <text v-if="isSelf(item.senderId)">你</text>
                  <text v-else>{{ item.senderData.name }}</text>
                  撤回了一条消息
                </view>
                <view class="recalled-edit" v-if="item.msgType === 'text' && isSelf(item.senderId)" @click="recalledEdit(item)">
                  重新编辑
                </view>
              </view>
            </view>
            <!-- #endif -->
          </block>
        </view>
        <view :style="{ height: $store.state.StatusBar.customBar - 8 + 4 + 'px' }"></view>
      </scroll-view>
      <view class="bottomOperationRef">
        <bottom-operation
          ref="bottomOperationRef"
          :to="to"
          :userInfo="userInfo"
          :userList="userList"
          :groupInfo="groupInfo"
          @pushList="handlePushList"
          @onBottom="onBottom"
          @backToBottom="bottomOperationScrollToBottom"
          @focus="focus"
          @keyboardheightchange="keyboardheightchange"
        ></bottom-operation>
      </view>
    </view>
    <!-- 视频播放器 -->
    <video-player-ref
      v-model="videoPlayer.show"
      :url="videoPlayer.url"
      @onVideoFullScreenChange="onVideoFullScreenChange"
    ></video-player-ref>
    <!-- 复制；撤回等操作 -->
    <operate
      ref="operateRef"
      @quote="quote"
      @thank="thank"
      @recallMessage="recallMessage"
      :userInfo="userInfo"
      @transmit="transmit"
    ></operate>
    <!-- 转发选择聊天 -->
    <m-group-selection ref="groupSelectionRef" @sendMessage="sendMessage"></m-group-selection>
  </view>
</template>
<script>
import navigation from './components/navigation/index.vue'
import bottomOperation from './components/bottom-operation/index.vue'
import item from './components/item/index'
import videoPlayerRef from './components/video-player/index'
import openRedPacket from './components/open-red-packet/index'
import operate from './components/operate/index'
import { mapState } from 'vuex'
import { msglist } from '../../api/public'
import mqttClient from '../../utils/mqttClient.js'
import mqtt from '../../lib/mqtt.min.js'
import store from '../../store/index.js'
import { SplitArray } from '../../utils'
import { throttle, openimg, getLocation, to as tofn } from '@/utils/index.js'
import { createUserInfo, TOPIC_TEMPLATES } from '../../utils/mqttConfig.js'

let lastMessageTimeStamp = null

let innerAudioContext = null
let audioItem = {}
let group = {}

// 安全初始化音频上下文
try {
  innerAudioContext = uni.createInnerAudioContext()
} catch (error) {
  console.warn('初始化音频上下文失败:', error)
  innerAudioContext = null
}

let groupId = null

// 浏览照片数组
let imageList = []

// 是否是手动触发的列表滑动
let isBottomOperationScrollToBottom = false

const IMAGE_MAX_WIDTH = 200
const IMAGE_MAX_HEIGHT = 150
let scroll_top = 0
let reserveHeightRef = 0
export default {
  components: {
    // groupSelection,
    navigation,
    bottomOperation,
    item,
    videoPlayerRef,
    openRedPacket,
    operate
  },
  name: 'groupChat',
  data() {
    return {
      isHistoryGet: false,
      reserveHeight: 0,
      keyboardheightchangeValue: 0,
      myid: null,
      scroll_top: 0,
      userList: [], //群成员列表
      groupCount: '',
      pagueObj: {
        name: '饭搭子5人组'
      },
      to: {},
      // 历史数据
      history: {
        messages: [],
        allLoaded: false
      },
      videoPlayer: {
        show: false,
        url: '',
        context: null
      },
      // 添加缺失的数据属性
      page: 1,
      pageSize: 50,
      groupId: '',
      loading: false,
      loadend: false,
      list: [],
      userMap: {},
      mqttClient: null,
      mqttPingInterval: null,
      userArr: [], //群成员
      groupInfo: {}, //群信息
      userInfo: {}, //用户信息
      scrollHeight: 0
    }
  },
  async onLoad(e) {
    console.log('🚀 ~ onLoad ~ e:', e)
    const groupInfo = JSON.parse(decodeURIComponent(e.groupInfo))
    this.groupInfo = groupInfo
    this.userInfo = JSON.parse(decodeURIComponent(e.userInfo))
    this.userArr = groupInfo.userArr
    // 设置MQTT库
    mqttClient.setMqttLib(mqtt)
    this.groupId = groupInfo.id
    //
    scroll_top = 0
    this.scroll_top = scroll_top
    imageList = []
    lastMessageTimeStamp = e.lastMessageTimeStamp || null
    this.isHistoryGet = e.lastMessageTimeStamp
    groupId = groupInfo.id
    //
    this.myid = this.userInfo.userId
    this.initMqtt()
    this.loadHistoryMessage()
    this.initScrollHeight()
    this.setHeight()
  },
  onPageScroll() {
    this.$refs.bottomOperationRef.closeAll()
  },
  onReady() {
    this.videoPlayer.context = uni.createVideoContext('videoPlayer', this)
  },

  onUnload() {
    // 页面卸载时清理资源
    mqttClient.disconnect()
    // 清理音频资源
    if (innerAudioContext && typeof innerAudioContext.destroy === 'function') {
      try {
        innerAudioContext.destroy()
      } catch (error) {
        console.warn('清理音频资源失败:', error)
      } finally {
        innerAudioContext = null
      }
    }
  },
  computed: mapState({
    page_font_size: (state) => state.page_font_size,
    //显示时间
    renderMessageDate() {
      return (message, index) => {
        // 检查 createTime 是否存在
        if (!message.createTime) {
          return ''
        }
        //正则替换 -
        const createTimeStamp = new Date(message.createTime.replace(/-/g, '/')).getTime()
        // 第一条消息总是显示时间
        if (index === 0) {
          return message.createTime
        }
        // 获取前一条消息
        const prevMessage = this.list[index - 1]
        if (prevMessage && prevMessage.createTime) {
          const prevCreateTimeStamp = new Date(prevMessage.createTime.replace(/-/g, '/')).getTime()
          // 如果当前消息比前一条消息晚3分钟以上，则显示时间
          if (createTimeStamp - prevCreateTimeStamp > 3 * 60 * 1000) {
            return message.createTime
          }
        }
        return ''
      }
    },
    // 是否本人isMy
    isSelf() {
      return (senderId) => {
        const { userId = '' } = this.userInfo
        return senderId === `${userId}`
      }
    },
    envelope_top_opened() {
      return (id) => {
        return this.envelopeXollectionList.includes(id)
      }
    }
  }),

  methods: {
    /**
     * 加载消息数据
     * @param {string} idEnd - 结束ID，用于分页加载
     * @returns {Promise<void>}
     */
    async loadHistoryMessage(idEnd = '') {
      // 防止重复加载和已加载完成的情况
      if (this.loading || this.loadend) {
        return
      }
      try {
        this.loading = true

        // 构建请求参数
        const requestData = {
          page: this.page,
          pageSize: this.pageSize,
          groupId: this.groupId,
          ...(idEnd && { id_end: idEnd })
        }

        const [ress, err] = await msglist(requestData)
        const res = ress.slice(30)
        if (err) {
          console.error('加载消息数据失败:', err)
          uni.showToast({
            title: '加载失败，请重试',
            icon: 'none'
          })
          return
        }

        if (res && Array.isArray(res)) {
          // 处理消息内容
          this.processMessages(res)
          // 合并数据到列表
          if (idEnd) {
            // 分页加载：将历史消息添加到列表开头
            this.list = SplitArray(res, []).concat(this.list)
          } else {
            // 初始加载：直接设置列表
            this.list = SplitArray(res, this.list)
          }
          // 检查是否已加载完所有数据
          this.loadend = res.length < this.pageSize
          console.log('消息数据加载成功:', res)
          // 如果不是分页加载，滚动到底部
          if (!idEnd) {
            this.$nextTick(() => {
              this.initContentHeight()
            })
          }
        }
      } catch (error) {
        console.error('loadData 异常:', error)
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    /**
     * 初始化MQTT连接
     */
    async initMqtt() {
      try {
        // 创建用户信息对象
        const mqttUserInfo = createUserInfo(
          this.userInfo.userId,
          this.userInfo.nickname || '用户',
          this.userInfo.channelCode,
          this.userInfo.token,
          this.userInfo.avatar || this.defaultAvatar,
          'DEV'
        )

        const callbacks = {
          onConnect: () => {
            console.log('MQTT连接成功')
            this.isConnected = true
            this.subscribe()
          },
          onMessage: (topic, mqttMsg) => {
            console.log('🚀 ~ initMqtt ~ topic, mqttMsg:', topic, mqttMsg)
            this.handleMqttMessage(mqttMsg)
          },
          onReconnect: () => {
            console.log('MQTT重连中...')
            this.isConnected = false
          },
          onError: (error) => {
            console.error('MQTT连接错误:', error)
            this.isConnected = false
          },
          onEnd: () => {
            console.log('MQTT连接已断开')
            this.isConnected = false
          }
        }
        // 连接MQTT
        mqttClient.connect(mqttUserInfo, callbacks)
      } catch (error) {
        console.error('初始化MQTT失败:', error)
        this.isConnected = false
      }
    },

    /**
     * 处理消息内容
     * @param {Array} messages - 消息列表
     */
    processMessages(messages) {
      messages.forEach((item) => {
        item.recalled = false
        const findUser = this.userArr.find((user) => user.userId === item.userId)
        // 处理发送者数据
        if (findUser) {
          item.senderData = {
            name: findUser.nickname,
            avatar: findUser.avatar,
            member_id: findUser.userId,
            group_id: item.groupId
          }
        }
        item.content = JSON.parse(item.content)
        // 处理语音消息内容
        if (item.msgType === 'voice') {
          console.log('🚀 ~ processMessages ~ item:', item)
          item.payload = { url: item.content.url, duration: item.content.duration }
        } else if (item.msgType === 'image') {
          item.payload = { url: item.content.url }
        } else if (item.msgType === 'withdraw') {
          item.recalled = true
          item.payload = { text: item.status }
        } else {
          item.payload = { text: item.content.msg }
        }
      })
    },

    /**
     * 获取音频文件时长
     * @param {string} audioUrl - 音频文件URL
     * @returns {Promise<number>} 音频时长（秒）
     */
    getAudioDuration(audioUrl) {
      return new Promise((resolve, reject) => {
        console.log('开始获取音频时长，URL:', audioUrl)

        // 在uni-app中使用createInnerAudioContext获取音频时长
        const audioContext = uni.createInnerAudioContext()
        let isResolved = false

        // 清理函数
        const cleanup = () => {
          if (audioContext && typeof audioContext.destroy === 'function') {
            try {
              audioContext.destroy()
            } catch (error) {
              console.warn('清理音频上下文失败:', error)
            }
          }
        }

        // 设置音频源
        audioContext.src = audioUrl
        console.log('已设置音频源:', audioUrl)

        // 监听音频加载完成事件
        audioContext.onCanplay(() => {
          if (isResolved) return

          // 获取音频时长
          const duration = audioContext.duration
          console.log('音频时长:', duration, '秒', 'URL:', audioUrl)

          cleanup()
          if (duration && duration > 0) {
            isResolved = true
            resolve(Math.ceil(duration)) // 向上取整
          } else {
            isResolved = true
            reject(new Error('无法获取音频时长'))
          }
        })

        // 监听音频加载事件（备用方案）
        audioContext.onLoadedmetadata &&
          audioContext.onLoadedmetadata(() => {
            if (isResolved) return

            const duration = audioContext.duration
            console.log('通过loadedmetadata获取音频时长:', duration, '秒')

            if (duration && duration > 0) {
              cleanup()
              isResolved = true
              resolve(Math.ceil(duration))
            }
          })

        // 监听错误事件
        audioContext.onError((error) => {
          if (isResolved) return

          console.error('音频加载失败:', error, 'URL:', audioUrl)
          cleanup()
          isResolved = true
          reject(error)
        })

        // 设置超时处理
        setTimeout(() => {
          if (!isResolved) {
            cleanup()
            isResolved = true
            reject(new Error('获取音频时长超时'))
          }
        }, 8000) // 8秒超时
      })
    },

    /**
     * 处理MQTT消息
     * @param {string} _topic - 消息主题（暂未使用）
     * @param {Buffer} message - 消息内容
     */
    handleMqttMessage(message) {
      if (message.command === 'chatMsg') {
        //普通消息
        this.processChatMessage(message.data)
      }
      if (message.command === 'withdraw') {
        //撤回
      }
      if (message.command === 'forbidden') {
        //禁言
      }
    },

    /**
     * 处理聊天消息
     * @param {Object} chatMsg - 聊天消息数据
     */
    processChatMessage(chatMsg) {
      if (this.groupId === chatMsg.groupId) {
        // 如果消息格式不完整，转换为标准格式
        const res = [chatMsg]
        this.processMessages(res)
        this.addMessageToList(res[0])
        // 标记消息为已读
        // this.markMessageAsRead(chatMsg.groupId)
      } else {
        // 其他群组消息，显示通知
      }
    },

    /**
     * 直接添加消息到列表（用于接收消息，不发送MQTT）
     * @param {Object} message - 消息对象
     */
    addMessageToList(message) {
      this.initMessageItem(message)
      // 监听到公告
      if (message.type === 'group_notice') {
        console.log('监听到公告')
        this.$refs.navigationRef.getData()
      }
      // 监听到修改群名
      if (message.type === 'update_group_name') {
        console.log('监听到修改群名')
        this.pagueObj.name = message.payload.name
      }
      // 将新消息添加到列表末尾（最新消息在底部）
      this.list.push(message)
      this.initContentHeight(true)
      // 是否触发文字动效果
      if (message.type === 'text' || message.type === 'text_quote') {
        this.onSetText(message.payload.text)
      }
      // 是否触发红包雨
      if (message.type === 'red_envelope') {
        this.onSetRedEnvelope()
      }

      // 缓存照片地址，
      if (message.type === 'image' || message.type === 'image_transmit') {
        imageList.push(message.payload.url)
      }
    },

    /**
     * 将旧格式消息转换为标准格式
     * @param {Object} oldMsg - 旧格式消息
     * @returns {Object} 标准格式消息
     */
    convertToStandardFormat(oldMsg) {
      const createTime = oldMsg.createTime
      const messageId = oldMsg.id
      return {
        content: oldMsg.content || oldMsg.payload?.text || '',
        createBy: null,
        createTime: createTime,
        groupId: oldMsg.groupId,
        id: messageId,
        msgType: oldMsg.msgType || oldMsg.type || 'text',
        payload: oldMsg.payload || { text: oldMsg.content },
        senderData: oldMsg.senderData || {
          avatar: this.userMap[oldMsg.userId]?.avatar || '',
          group_id: oldMsg.groupId,
          member_id: oldMsg.userId,
          name: this.userMap[oldMsg.userId]?.nickname || oldMsg.nickname || ''
        },
        status: '正常',
        sysOrgCode: null,
        updateBy: null,
        updateTime: null,
        userId: oldMsg.userId
      }
    },

    /**
     * 标记消息为已读
     * @param {string} groupId - 群组ID
     */
    markMessageAsRead(groupId) {
      if (this.mqttClient && this.mqttClient.connected) {
        const userInfo = store.state.app.userInfo
        const readMessage = JSON.stringify({ groupId })
        this.mqttClient.publish(`/chat/server/${userInfo.userId}/read`, readMessage)
      }
    },

    /**
     * 清除心跳定时器
     */
    clearPingInterval() {
      if (this.mqttPingInterval) {
        clearInterval(this.mqttPingInterval)
        this.mqttPingInterval = null
      }
    },

    /**
     * 断开MQTT连接
     */
    disconnectMqtt() {
      this.clearPingInterval()

      if (this.mqttClient) {
        this.mqttClient.end()
        this.mqttClient = null
      }
    },
    setHeight(e) {
      // 只计算导航栏的高度
      const customBar = this.$store.state.StatusBar.customBar
      this.reserveHeight = customBar
      reserveHeightRef = customBar
    },
    // getHeight(e) {
    //   this.$nextTick(() => {
    //     let view = uni.createSelectorQuery().select('.messageList_')
    //     view
    //       .boundingClientRect((select) => {
    //         if (!select) return
    //         if (!select?.height) {
    //           this.$nextTick(() => {
    //             let view2 = uni.createSelectorQuery().select('.messageList_')
    //             view2
    //               .boundingClientRect((select) => {
    //                 this.setHeight(select.height)
    //                 if (e) {
    //                   setTimeout(() => {
    //                     this.reserveHeight = this.reserveHeight - this.keyboardheightchangeValue
    //                   })
    //                 }
    //               })
    //               .exec()
    //           })
    //         } else {
    //           this.setHeight(select.height)
    //           if (e) {
    //             setTimeout(() => {
    //               this.reserveHeight = this.reserveHeight - this.keyboardheightchangeValue
    //             })
    //           }
    //         }
    //       })
    //       .exec()
    //   })
    // },

    //图片加载完成
    imgLoad() {
      if (this.list.length > 20) return
      this.initContentHeight(true)
    },
    keyboardheightchange(e, e2 = false) {
      this.keyboardheightchangeValue = e
      if (reserveHeightRef) {
        this.reserveHeight = reserveHeightRef - e
      }
      if (e === 0) {
        if (e2) return
        this.initContentHeight()
      }
    },

    // 点击整个页面
    onPage() {
      this.$refs.bottomOperationRef.close()
      this.$refs.operateRef.close()
    },
    touchmove() {
      // this.$refs.bottomOperationRef.closeAll();
    },
    onBottom() {
      this.$refs.operateRef.close()
    },
    // 输入框获取焦点
    focus() {
      if (this.isHistoryGet) {
        this.isHistoryGet = false
        lastMessageTimeStamp = null
        this.list = []
        this.loadHistoryMessage()
      }
    },
    onMessageReceived(message) {
      if (message.groupId === group.id) {
        // push进列表
        this.pushList(message)
        //聊天时，收到消息标记为已读
        this.markGroupMessageAsRead()
      }
    },
    // 转发成功后
    sendMessage(message) {
      // push进列表
      if (message.groupId === groupId) {
        this.pushList(message)
        // 同步消息到首页
        uni.$emit('onMessageReceived', message)
      }
    },
    // 将信息设置为已读
    markGroupMessageAsRead() {
      //
    },
    // 组装item
    initMessageItem(message, index) {
      message['isHide'] = 0
      // 初始化语音
      if (message.type === 'audio') {
        message['pause'] = 4
      }
    },
    //
    async handlePushList(message) {
      // 发送MQTT消息到服务器
      this.publishMessageToMqtt(message)
    },
    // 发送信息后，将信息push到列表
    async pushList(message) {
      this.initMessageItem(message)
      // 监听到公告
      if (message.type === 'group_notice') {
        console.log('监听到公告')
        this.$refs.navigationRef.getData()
      }
      // 监听到修改群名
      if (message.type === 'update_group_name') {
        console.log('监听到修改群名')
        this.pagueObj.name = message.payload.name
      }
      // 将新消息添加到列表末尾（最新消息在底部）
      this.list.push(message)
      this.initContentHeight(true)
      // 是否触发文字动效果
      if (message.type === 'text' || message.type === 'text_quote') {
        this.onSetText(message.payload.text)
      }
      // 是否触发红包雨
      if (message.type === 'red_envelope') {
        this.onSetRedEnvelope()
      }
      // 缓存照片地址，
      if (message.type === 'image' || message.type === 'image_transmit') {
        imageList.push(message.payload.url)
      }
    },

    /**
     * 发布消息到MQTT队列
     * @param {Object} message - 消息对象
     */
    async publishMessageToMqtt(message) {
      console.log('🚀 ~ publishMessageToMqtt ~ message:', message)
      try {
        // 使用已创建的MQTT客户端实例
        if (!mqttClient.getConnectStatus()) {
          console.warn('MQTT未连接，无法发送消息')
          return false
        }
        // 构建MQTT消息格式
        const mqttMessage = {
          groupId: message.groupId,
          msgType: message.msgType,
          content: message.content,
          localMsgId: message.id,
          userId: message.userId
        }
        //  const mqttMessage = {
        //   data: {
        //     groupId: message.groupId,
        //     msgType: message.msgType,
        //     content: message.content,
        //     localMsgId: message.id,
        //     userId: message.userId
        //   },
        //   command: 'chatMsg' //chatMsg 为聊天内容, withdraw为撤回消息的推送
        // }
        // 发布到服务器主题
        const topic = `/chat/server/${this.userInfo.userId}/msg`
        console.log('🚀 ~ publishMessageToMqtt ~ topic:', topic)
        const success = mqttClient.publish(topic, mqttMessage)
        console.log('🚀 ~ publishMessageToMqtt ~ success:', success)
        if (success) {
          console.log('MQTT消息发送成功:', mqttMessage)
        } else {
          console.error('MQTT消息发送失败')
        }

        return success
      } catch (error) {
        console.error('发送MQTT消息异常:', error)
        return false
      }
    },
    /**
     * 撤回消息
     * @param text
     */
    async recallMessage(item) {
      console.log('🚀 ~ recallMessage ~ item:', item)
      try {
        // 使用已创建的MQTT客户端实例
        if (!mqttClient.getConnectStatus()) {
          console.warn('MQTT未连接，无法发送消息')
          return false
        }
        // 构建MQTT消息格式
        const mqttMessage = {
          id: item.id, //这是消息的ID
          groupId: item.groupId //群ID
        }
        // 发布到服务器主题
        const topic = `/chat/server/${this.userInfo.userId}/withdraw`
        const success = mqttClient.publish(topic, mqttMessage)
        if (success) {
          console.log('MQTT消息发送成功:', mqttMessage)
          this.list.forEach((i) => {
            console.log('🚀 ~ recallMessage ~ i:', i.id === item.id)
            if (i.id === item.id) {
              i.recalled = true
            }
          })
        } else {
          console.error('MQTT消息发送失败')
        }

        return success
      } catch (error) {
        console.error('发送MQTT消息异常:', error)
        return false
      }
    },
    //订阅消息
    async subscribe() {
      const topic = `/chat/client/${this.userInfo.userId}`
      const success = mqttClient.subscribe(topic, { qos: 0 }, (err) => {
        if (!success) {
          console.log(`✅ 订阅成功: ${topic}`)
        } else {
          console.error(`❌ 订阅失败: ${topic}`, err)
        }
      })
    },

    // 文本触发效果相关========
    onSetText(text) {
      // 触发礼花
      throttle(() => {
        if (text.includes('[彩带]')) {
          this.$refs.mScreenAnimationLihua.show()
          uni.vibrateLong()
        }
      }, 4000)
    },
    // 触发红包雨
    onSetRedEnvelope() {
      throttle(() => {
        uni.vibrateLong()
      }, 4000)
    },
    bottomOperationScrollToBottom() {
      isBottomOperationScrollToBottom = true
      setTimeout(() => {
        isBottomOperationScrollToBottom = false
        this.initContentHeight()
      }, 800)
    },
    // 点击某条信息
    onItem(item) {
      console.log(item)
      switch (item.msgType) {
        case 'video':
          this.playVideo(item)
          break
        case 'voice':
          this.playAudio(item)
          break
        case 'audio_quote':
          this.playAudio(item)
          break
        case 'image':
        case 'image_transmit':
          const index = imageList.indexOf(item.payload.url)
          if (index === -1) return openimg(imageList.length - 1, imageList)
          openimg(index, imageList)
          break
        case 'red_envelope':
          // 点击红包
          const fun = (code) => {
            this.renewItem(code, item)
          }
          uni.$off('open_red_packet')
          uni.$on('open_red_packet', fun)
          item['id'] = group.id
          break
        case 'map':
          getLocation({
            name: item.payload.title,
            address: item.payload.address,
            latitude: item.payload.latitude,
            longitude: item.payload.longitude
          })
          break
        case 'article':
          tofn(`/pagesOne/HTML/index?id=${item.payload.id}`)
          break
        case 'share_SBCF':
          tofn('/pagesSBCF/commodity_list/index', {
            id: item.payload.seller_id
          })
          break
        case 'share_mall':
          tofn(`/pagesShopping/details/index`, {
            goods_id: item.payload.goods_id
          })
          break
        case 'functional_module':
          tofn(item.payload.url)
          break
        default:
          break
      }
    },
    // 点击红包后更新那一条
    renewItem(code, item) {
      if (code === '0') {
        // 领取
        item.had_draw = 1
      } else {
        item.isClick = 1
      }
      // 不这样写某些情况下更新不了视图，
      for (let i = 0; i < this.list.length; i++) {
        if (this.list[i].messageId == item.messageId) {
          this.$set(this.list, i, {
            ...item
          })
          break
        }
      }
    },
    // 长按相关=======================
    // 长按某一条
    onLongpress(item, e) {
      this.$refs.operateRef.open(item, e)
    },
    // 引用
    quote(item) {
      this.$refs.bottomOperationRef.quote(item)
    },
    // 谢谢红包
    thank(item) {
      this.$refs.bottomOperationRef.thank(item)
    },
    // 转发
    transmit(item) {
      this.$refs.groupSelectionRef.open(item)
    },
    // 重新编辑
    recalledEdit(item) {
      this.$refs.bottomOperationRef.recalledEdit(item)
    },
    // @某人
    mention(item) {
      this.$refs.bottomOperationRef.mention(item)
    },
    // 视频相关========================
    // 点击了视频并播放
    playVideo(item) {
      this.videoPlayer.url = item.payload.video.url
      this.videoPlayer.show = true
      this.$nextTick(() => {
        this.videoPlayer.context.requestFullScreen({
          direction: 0
        })
        this.videoPlayer.context.play()
        this.videoPlayer.context.showStatusBar()
      })
    },
    // 退出全屏
    onVideoFullScreenChange(e) {
      //当退出全屏播放时，隐藏播放器
      if (this.videoPlayer.show && !e.detail.fullScreen) {
        this.videoPlayer.show = false
        this.videoPlayer.context.stop()
      }
    },
    // =============================================
    // 播放语音相关===========
    playAudio(item) {
      throttle(() => {
        // pause:1暂停;2播放完,3播放中,4初始状态
        if (item.id === audioItem?.id) {
          if (audioItem['pause'] == 3) {
            //正在播放
            // 暂停
            innerAudioContext.pause()
            innerAudioContext.offEnded()
            item['pause'] = 1
            audioItem['pause'] = 1
          } else if (audioItem['pause'] == 1 || audioItem['pause'] == 2) {
            //暂停或者播放中
            // 播放
            innerAudioContext.play()
          }
          return
        }
        audioItem['pause'] = '4'
        audioItem = item
        if (innerAudioContext) {
          try {
            if (typeof innerAudioContext.pause === 'function') {
              innerAudioContext.pause()
            }
            if (typeof innerAudioContext.destroy === 'function') {
              innerAudioContext.destroy()
            }
          } catch (e) {
            console.warn('清理音频上下文失败:', e)
          } finally {
            innerAudioContext = null
          }
        }

        // 安全创建新的音频上下文
        try {
          innerAudioContext = uni.createInnerAudioContext()
        } catch (error) {
          console.error('创建音频上下文失败:', error)
          return
        }

        if (!innerAudioContext) {
          console.error('音频上下文创建失败')
          return
        }

        console.log('准备播放音频，URL:', item.payload.url)
        console.log('音频item信息:', item)

        innerAudioContext.src = item.payload.url
        innerAudioContext.play()
        innerAudioContext.offEnded()
        innerAudioContext.offPlay()
        innerAudioContext.onPlay(() => {
          console.log('开始播放音频:', item.payload.url)
          item['pause'] = 3
          audioItem['pause'] = 3
        })
        innerAudioContext.onEnded(() => {
          console.log('音频播放结束:', item.payload.url)
          item['pause'] = 2
          audioItem['pause'] = 2
        })
        innerAudioContext.onError((res) => {
          console.error('音频播放异常:', res, 'URL:', item.payload.url)
        })
      }, 500)
    },

    // ====================
    // 滚动中
    scroll(e) {
      scroll_top = e.detail.scrollTop
      this.$refs.operateRef.close()
      if (isBottomOperationScrollToBottom) return
      this.$refs.bottomOperationRef.closeAll()
    },
    // 滚动到底部
    scrolltolower() {
      if (this.history.allLoaded) return
      console.log('触底')
      this.loadHistoryMessage()
    },
    // 滚动到顶部
    scrolltoupper() {
      if (this.loading || this.loadend) return
      console.log('滚动到顶部，加载更多历史消息')
      // 获取第一条消息的ID作为分页参数
      const firstMessage = this.list[0]
      if (firstMessage) {
        this.loadData(firstMessage.id)
      }
    },
    initScrollHeight() {
      this.$nextTick(() => {
        uni
          .createSelectorQuery()
          .in(this)
          .select('.scroll-view')
          .boundingClientRect((data) => {
            if (data) {
              this.scrollHeight = data.height
            }
          })
          .exec()
      })
    },

    // 获取内容高度
    initContentHeight() {
      uni
        .createSelectorQuery()
        .in(this)
        .select('.messageList_')
        .boundingClientRect((data) => {
          if (data) {
            let top = data.height - this.scrollHeight
            console.log('🚀 ~ initContentHeight ~ top:', top)
            if (top > 0) {
              this.$nextTick(() => {
                this.scroll_top = top
              })
            }
          }
        })
        .exec()
    },
    /**
     *  根据用户id获取用户昵称
     * @param userId 用户id
     * @returns 用户昵称
     */
    getByIdUser(userId) {
      return this.userArr.find((user) => user.userId === userId)?.nickname || ''
    },
    onMore() {
      uni.navigateTo({
        url: `/pagesHuYunIm/pages/group_infor/index?groupInfo=${encodeURIComponent(
          JSON.stringify(this.groupInfo)
        )}&userInfo=${encodeURIComponent(JSON.stringify(this.userInfo))}`
      })
    },
    handleItem(item) {
      const findUser = this.userArr.find((user) => user.userId === item.userId)
      if (!findUser) return
      uni.navigateTo({
        url: `/pagesHuYunIm/pages/group_member_infor/index?memberInfo=${JSON.stringify(findUser)}&groupInfo=${JSON.stringify(
          this.groupInfo
        )}`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.page {
  position: fixed;
  z-index: 1;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: #ededed;
}

.scroll-Y {
  width: 100%;
  height: 0;
  transition: all 0.2s;
  background-color: #ededed;

  ::-webkit-scrollbar {
    display: none;
  }
}

.scroll-view-str {
  width: 100%;
}

.time {
  width: 100%;
  color: #a3a3a3;
  line-height: 100rpx;
}

.recalled {
  width: 100%;
  height: 50rpx;
  margin: 20rpx 0;
  color: #a3a3a3;

  .recalled-edit {
    color: #5a6693;
    margin-left: 14rpx;
  }
}
</style>
