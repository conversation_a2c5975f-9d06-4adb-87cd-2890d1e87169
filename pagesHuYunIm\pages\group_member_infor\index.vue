<template>
  <view>
    <view style="height: 20rpx"></view>
    <view class="flex_r fa_c infor">
      <view class="infor-img-box" @click="openimg(senderData.avatar)">
        <view class="avatar_pendant" v-if="senderData.avatar">
          <image class="img" :src="senderData.avatar" mode="aspectFill"></image>
        </view>
        <view class="infor-img">
          <image class="img" src="" mode="aspectFill"></image>
        </view>
      </view>
      <view class="flex1 infor-r">
        <view class="flex_r fa_c infor-r-name">
          <view class="text_34 bold_">{{ senderData.nickname }}</view>
          <!-- <view class="text_20 color__ label">{{ pageObj.group_name }}</view> -->
        </view>
        <view class="text_26 color__">群昵称：{{ groupInfo.title }}</view>
        <view class="text_26 color__">ID：{{ senderData.userId }}</view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <view class="action-buttons">
        <view class="action-btn mute-btn" @click="handleMute">
          <view class="btn-icon">
            <image class="icon-img" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyUzYuNDggMjIgMTIgMjJTMjIgMTcuNTIgMjIgMTJTMTcuNTIgMiAxMiAyWk0xNyAxM0g3VjExSDEzVjEzWiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K" mode="aspectFill"></image>
          </view>
          <text class="btn-text">禁言</text>
        </view>
        <view class="action-btn delete-btn" @click="handleDelete">
          <view class="btn-icon">
            <image class="icon-img" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYgMTlDNiAyMC4xIDYuOSAyMSA4IDIxSDE2QzE3LjEgMjEgMTggMjAuMSAxOCAxOVY3SDZWMTlaTTggOUgxNlYxOUg4VjlaTTE1LjVMMTMuNSA0SDE0LjVIMTAuNUw4LjUgNkg0VjhIMjBWNkgxNS41WiIgZmlsbD0iI0U0M0QzMyIvPgo8L3N2Zz4K" mode="aspectFill"></image>
          </view>
          <text class="btn-text">删除</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
let data = null
export default {
  data() {
    return {
      senderData: {},
      groupInfo: {}
    }
  },
  onLoad(e) {
    console.log('🚀 ~ onLoad ~ e:', e)
    this.senderData = JSON.parse(e.memberInfo)
    this.groupInfo = JSON.parse(e.groupInfo)
  },
  methods: {
    openimg(index, item, attributes = '') {
      if (item) {
        // 数组对象请况
        if (attributes) {
          let arr = []
          item.forEach((item, ix) => {
            if (item[attributes]) {
              arr.push(item[attributes])
            }
          })
          uni.previewImage({
            urls: arr,
            current: arr[index]
          })
        } else {
          // 数组请况
          uni.previewImage({
            urls: item,
            current: item[index]
          })
        }
      } else if (!item) {
        //传入单张照片
        let arr = []
        arr.push(index)
        uni.previewImage({
          urls: arr,
          current: arr[1]
        })
      }
    },

    // 处理禁言操作
    handleMute() {
      uni.showModal({
        title: '禁言确认',
        content: `确定要禁言 ${this.senderData.nickname} 吗？`,
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.muteUser()
          }
        }
      })
    },

    // 处理删除操作
    handleDelete() {
      uni.showModal({
        title: '删除确认',
        content: `确定要将 ${this.senderData.nickname} 移出群聊吗？`,
        confirmText: '确定',
        cancelText: '取消',
        confirmColor: '#E43D33',
        success: (res) => {
          if (res.confirm) {
            this.deleteUser()
          }
        }
      })
    },

    // 禁言用户
    async muteUser() {
      try {
        uni.showLoading({
          title: '处理中...'
        })

        // TODO: 调用禁言API
        // const result = await this.API_muteUser(this.senderData.userId, this.groupInfo.id)

        // 模拟API调用
        setTimeout(() => {
          uni.hideLoading()
          uni.showToast({
            title: '禁言成功',
            icon: 'success'
          })
          // 可以在这里更新用户状态或返回上一页
        }, 1000)

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '禁言失败',
          icon: 'error'
        })
      }
    },

    // 删除用户
    async deleteUser() {
      try {
        uni.showLoading({
          title: '处理中...'
        })

        // TODO: 调用删除API
        // const result = await this.API_kickUser(this.senderData.userId, this.groupInfo.id)

        // 模拟API调用
        setTimeout(() => {
          uni.hideLoading()
          uni.showToast({
            title: '移除成功',
            icon: 'success'
          })
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }, 1000)

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '移除失败',
          icon: 'error'
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.infor {
  box-sizing: border-box;
  padding: 30rpx;
  width: 100%;
  .infor-img-box {
    position: relative;
    margin-right: 20rpx;
    .infor-img {
      width: 150rpx;
      height: 150rpx;
      border-radius: 20rpx;
      overflow: hidden;
      background-color: #f1f1f1;
    }
    .avatar_pendant {
      position: absolute;
      z-index: 3;
      top: -2px;
      left: -2px;
      bottom: -2px;
      right: -2px;
    }
  }

  .infor-r {
    .infor-r-name {
      width: 100%;
      .label {
        box-sizing: border-box;
        padding: 0 10rpx;
        // height: 38rpx;
        line-height: 38rpx;
        margin-left: 20rpx;
        border-radius: 30rpx;
        border: 1px solid #999;
      }
    }
  }
}
</style>
