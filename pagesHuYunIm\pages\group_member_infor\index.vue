<template>
  <view>
    <view style="height: 20rpx"></view>
    <view class="flex_r fa_c infor">
      <view class="infor-img-box" @click="openimg(senderData.avatar)">
        <view class="avatar_pendant" v-if="senderData.avatar">
          <image class="img" :src="senderData.avatar" mode="aspectFill"></image>
        </view>
        <view class="infor-img">
          <image class="img" src="" mode="aspectFill"></image>
        </view>
      </view>
      <view class="flex1 infor-r">
        <view class="flex_r fa_c infor-r-name">
          <view class="text_34 bold_">{{ senderData.nickname }}</view>
          <!-- <view class="text_20 color__ label">{{ pageObj.group_name }}</view> -->
        </view>
        <view class="text_26 color__">群昵称：{{ groupInfo.title }}</view>
        <view class="text_26 color__">ID：{{ senderData.userId }}</view>
      </view>
    </view>
  </view>
</template>

<script>
let data = null
export default {
  data() {
    return {
      senderData: {},
      groupInfo: {}
    }
  },
  onLoad(e) {
    console.log('🚀 ~ onLoad ~ e:', e)
    this.senderData = JSON.parse(e.memberInfo)
    this.groupInfo = JSON.parse(e.groupInfo)
  },
  methods: {
    openimg(index, item, attributes = '') {
      if (item) {
        // 数组对象请况
        if (attributes) {
          let arr = []
          item.forEach((item, ix) => {
            if (item[attributes]) {
              arr.push(item[attributes])
            }
          })
          uni.previewImage({
            urls: arr,
            current: arr[index]
          })
        } else {
          // 数组请况
          uni.previewImage({
            urls: item,
            current: item[index]
          })
        }
      } else if (!item) {
        //传入单张照片
        let arr = []
        arr.push(index)
        uni.previewImage({
          urls: arr,
          current: arr[1]
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.infor {
  box-sizing: border-box;
  padding: 30rpx;
  width: 100%;
  .infor-img-box {
    position: relative;
    margin-right: 20rpx;
    .infor-img {
      width: 150rpx;
      height: 150rpx;
      border-radius: 20rpx;
      overflow: hidden;
      background-color: #f1f1f1;
    }
    .avatar_pendant {
      position: absolute;
      z-index: 3;
      top: -2px;
      left: -2px;
      bottom: -2px;
      right: -2px;
    }
  }

  .infor-r {
    .infor-r-name {
      width: 100%;
      .label {
        box-sizing: border-box;
        padding: 0 10rpx;
        // height: 38rpx;
        line-height: 38rpx;
        margin-left: 20rpx;
        border-radius: 30rpx;
        border: 1px solid #999;
      }
    }
  }
}
</style>
