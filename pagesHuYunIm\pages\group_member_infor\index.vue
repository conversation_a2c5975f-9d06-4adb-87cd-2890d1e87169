<template>
  <view>
    <view style="height: 20rpx"></view>
    <view class="flex_r fa_c infor">
      <view class="infor-img-box" @click="openimg(senderData.avatar)">
        <view class="avatar_pendant" v-if="senderData.avatar">
          <image class="img" :src="senderData.avatar" mode="aspectFill"></image>
        </view>
        <view class="infor-img">
          <image class="img" src="" mode="aspectFill"></image>
        </view>
      </view>
      <view class="flex1 infor-r">
        <view class="flex_r fa_c infor-r-name">
          <view class="text_34 bold_">{{ senderData.nickname }}</view>
          <!-- <view class="text_20 color__ label">{{ pageObj.group_name }}</view> -->
        </view>
        <view class="text_26 color__">群昵称：{{ groupInfo.title }}</view>
        <view class="text_26 color__">ID：{{ senderData.userId }}</view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <view class="action-buttons">
        <view class="action-btn mute-btn" @click="handleMute">
          <view class="btn-content">
            <view class="btn-icon">
              <text class="icon-text">🔇</text>
            </view>
            <text class="btn-text">禁言</text>
          </view>
        </view>
        <view class="action-btn delete-btn" @click="handleDelete">
          <view class="btn-content">
            <view class="btn-icon">
              <text class="icon-text">🗑️</text>
            </view>
            <text class="btn-text">删除</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
let data = null
export default {
  data() {
    return {
      senderData: {},
      groupInfo: {}
    }
  },
  onLoad(e) {
    console.log('🚀 ~ onLoad ~ e:', e)
    this.senderData = JSON.parse(e.memberInfo)
    this.groupInfo = JSON.parse(e.groupInfo)
  },
  methods: {
    openimg(index, item, attributes = '') {
      if (item) {
        // 数组对象请况
        if (attributes) {
          let arr = []
          item.forEach((item, ix) => {
            if (item[attributes]) {
              arr.push(item[attributes])
            }
          })
          uni.previewImage({
            urls: arr,
            current: arr[index]
          })
        } else {
          // 数组请况
          uni.previewImage({
            urls: item,
            current: item[index]
          })
        }
      } else if (!item) {
        //传入单张照片
        let arr = []
        arr.push(index)
        uni.previewImage({
          urls: arr,
          current: arr[1]
        })
      }
    },

    // 处理禁言操作
    handleMute() {
      uni.showModal({
        title: '禁言确认',
        content: `确定要禁言 ${this.senderData.nickname} 吗？`,
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.muteUser()
          }
        }
      })
    },

    // 处理删除操作
    handleDelete() {
      uni.showModal({
        title: '删除确认',
        content: `确定要将 ${this.senderData.nickname} 移出群聊吗？`,
        confirmText: '确定',
        cancelText: '取消',
        confirmColor: '#E43D33',
        success: (res) => {
          if (res.confirm) {
            this.deleteUser()
          }
        }
      })
    },

    // 禁言用户
    async muteUser() {
      try {
        uni.showLoading({
          title: '处理中...'
        })

        // TODO: 调用禁言API
        // const result = await this.API_muteUser(this.senderData.userId, this.groupInfo.id)

        // 模拟API调用
        setTimeout(() => {
          uni.hideLoading()
          uni.showToast({
            title: '禁言成功',
            icon: 'success'
          })
          // 可以在这里更新用户状态或返回上一页
        }, 1000)

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '禁言失败',
          icon: 'error'
        })
      }
    },

    // 删除用户
    async deleteUser() {
      try {
        uni.showLoading({
          title: '处理中...'
        })

        // TODO: 调用删除API
        // const result = await this.API_kickUser(this.senderData.userId, this.groupInfo.id)

        // 模拟API调用
        setTimeout(() => {
          uni.hideLoading()
          uni.showToast({
            title: '移除成功',
            icon: 'success'
          })
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }, 1000)

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '移除失败',
          icon: 'error'
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.infor {
  box-sizing: border-box;
  padding: 30rpx;
  width: 100%;
  .infor-img-box {
    position: relative;
    margin-right: 20rpx;
    .infor-img {
      width: 150rpx;
      height: 150rpx;
      border-radius: 20rpx;
      overflow: hidden;
      background-color: #f1f1f1;
    }
    .avatar_pendant {
      position: absolute;
      z-index: 3;
      top: -2px;
      left: -2px;
      bottom: -2px;
      right: -2px;
    }
  }

  .infor-r {
    .infor-r-name {
      width: 100%;
      .label {
        box-sizing: border-box;
        padding: 0 10rpx;
        // height: 38rpx;
        line-height: 38rpx;
        margin-left: 20rpx;
        border-radius: 30rpx;
        border: 1px solid #999;
      }
    }
  }
}

// 操作按钮区域样式
.action-section {
  margin-top: 80rpx;
  padding: 0 40rpx;
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 60rpx;
}

.action-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 280rpx;
  height: 120rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:active {
    transform: translateY(2rpx) scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);

    &::before {
      opacity: 1;
    }
  }
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.btn-icon {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.icon-text {
  font-size: 32rpx;
  line-height: 1;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

// 禁言按钮特殊样式
.mute-btn {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-color: #dee2e6;

  .btn-icon {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  }

  .icon-text {
    filter: grayscale(1) brightness(1.2);
  }

  .btn-text {
    color: #6c757d;
  }

  &:active {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);

    .btn-text {
      color: #495057;
    }
  }
}

// 删除按钮特殊样式
.delete-btn {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-color: #feb2b2;

  .btn-icon {
    background: linear-gradient(135deg, #fc8181 0%, #e53e3e 100%);
  }

  .btn-text {
    color: #e53e3e;
  }

  &:active {
    background: linear-gradient(135deg, #fed7d7 0%, #fc8181 100%);

    .btn-text {
      color: #c53030;
    }
  }
}
</style>
