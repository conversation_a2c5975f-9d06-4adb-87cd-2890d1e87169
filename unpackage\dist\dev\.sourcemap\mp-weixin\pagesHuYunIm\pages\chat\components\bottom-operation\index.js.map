{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?0125", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?85c4", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?e01f", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?e088", "uni-app:///pagesHuYunIm/pages/chat/components/bottom-operation/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?4e4a", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?388a"], "names": ["components", "emoji", "more", "mRecorder", "memberSelectionLoading", "mText", "mImage", "mAudio", "mOther", "props", "to", "type", "default", "userInfo", "groupInfo", "userList", "isPrivate", "data", "a", "a_b", "b", "c", "isFocus", "isKeyboard", "is<PERSON><PERSON><PERSON>", "isMore", "isRecorder", "isCancel", "text", "keyboardHeight", "isQuote", "quoteSource", "keyHeight", "created", "uni", "console", "<PERSON><PERSON><PERSON><PERSON>", "cursor", "clearInterval", "methods", "setCursor", "getSelectedTextRangeSetInterval", "success", "fail", "backToBottom", "closeAll", "onBottom", "recalledEdit", "close", "onKeyboard", "keyboardheightchange", "<PERSON><PERSON><PERSON><PERSON>", "tapMore", "on<PERSON><PERSON><PERSON>", "focus", "handleBlur", "input", "inputValue", "lineBreak", "itemclick", "deleteFn", "del", "xstr", "quote", "itemx", "thank", "mention", "setTimeout", "cancelQuote", "recorderTop", "initRecorderListeners", "recorder<PERSON>anager", "startTime", "res", "touchstart", "format", "touchmove", "touchend", "onMore", "title", "openShot", "plug", "setMaxduration", "SpeedColor", "ratio", "mp4", "duration", "size", "image", "errMsg", "tempFile<PERSON>ath", "width", "height", "path", "sendingText", "showCancel", "content", "body", "payload", "createCustomMessageMap", "longitude", "address", "name", "latitude", "createCustomMessageText", "sendImageMessage", "count", "request", "uploadFile", "then", "file", "catch", "createImageMessage", "contentType", "url", "thumbnail", "sendVideoMessage", "createVideoMessage", "video", "sendingRecorder", "sendingEmojiPack", "ext", "sendMessage", "msg", "createBy", "createTime", "groupId", "id", "msgType", "senderData", "avatar", "group_id", "member_id", "status", "sysOrgCode", "updateBy", "updateTime", "userId", "recalled", "isHide"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACuH5wB;AASA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AAAA,gBACA;EACAA;IACAC;IACAC;IACAC;IACA;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACAC;IACAA;;IAEA;IACAA;IACAA;IAGAA;MACAC;MACA;IACA;EAGA;EACAC;IACAF;IACAA;IACAG;IACAC;EACA;EACAC;IACAC;MACAC;QACAP;UACAQ;YACAL;UACA;UACAM;YACAL;UACA;QACA;MACA;IACA;IACA;IACAM;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MAIA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MAgBA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;MAIA;IACA;IACAC;MACA;MACA;MACA;MAIA;IACA;IACAC;MACA;MACA;IACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MAOA;MACA;MACA;MAEAhB;MACA;IACA;IACAiB;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA,2CACA;QACA;QACA;UACA;UACA;UACA;YACA;UACA;QACA;MACA;MACAC;IACA;IACA;IACAC;MAAA;MACAvB;MACAA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAwB;MAAA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QAAA,IAQAC;UACA;QACA;QATA;QACA;QACA;UACAC;QACA;QACA;QAKA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;UACA;QACA;MACA;MACA;QACA;MACA;QACA;MAAA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACAC;QACA;QACAC;MACA;MACA;MACAD;QACA;QACA;QACA;QACA;QACA;QACAE;QACA;QACA;MACA;MACA;MACAF;QACA;QACAA;QACA;MACA;IACA;IACA;IACAG;MACA;MACA;MACA;QACAH;UAAAI;QAAA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACAN;MACA;QACApC;MACA;MACA;IACA;IACA;IACA;IACA2C;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA5C;UACAA;;UAEA;UACA;YACA,gFACA,SACA;UACA;YACA,wEACA,SACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACAA;YACAQ;cAAA;gBAAA;kBAAA;oBAAA;sBAAA;wBACAP;wBACAD;0BACA6C;wBACA;wBACA;0BACA;wBACA;wBACA7C;sBAAA;sBAAA;wBAAA;oBAAA;kBAAA;gBAAA;cAAA,CACA;cAAA;gBAAA;cAAA;cAAA;YAAA;YACAS;cACAR;YACA;UACA;UACA;QACA;UACA;MAAA;IAEA;IACA6C;MAAA;MACA;MACA;MAEA;MACAC,UACA;QACAC;QACAC;QACAC;MACA,GACA;QACA;UAAA;UAAAC;UAAA;UAAAC;UAAA;UAAAC;UAAAC;QACA;UACA;YACAC;YACAC;YACAH;YACAD;YAAA;YACAK;YACAC;UACA;UACA;QACA;UACA;YACAL;YACAM;UACA;QACA;QACA;QACA,gCACA;MACA,EACA;IACA;IACA;IACA;IACAC;MACA,sBACA;QACAC;QACAC;QACAtD;MACA;MACA;MACA;QACAuD;MACA;MACA;QACA;QACA;MACA;MACA;QACAC;UACAtE;QACA;QACAjB;MACA;MAEA;IACA;IAEA;IACAwF;MACA;QAAAC;QAAAC;QAAAC;MACA;QACAJ;UACAK;UACAH;UACArB;UACAsB;UACAb;QACA;;QAEA7E;MACA;IACA;IAEA;IACA6F;MACA;QACAN;UACAtE;UACA;UACAG,+BACA;QAEA;QACApB;MACA;MAEA;IACA;IAEA;IACA8F;MAAA;MACAvE;QACAwE;QACAhE;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBACA+B;sBACAtC;sBACAwE,iBACAC,sBACAC;wBACA1E;wBACA2E;wBACA;sBACA,GACAC;wBACA5E;sBACA;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IACA;IACA6E;MACA7E;MACA;QACA+D;UACAe;UACAX;UACAf;UACA2B;UACAvB;UACAC;UACAuB;QACA;QACAxG;MACA;IACA;IACA;IACAyG;MAAA;MACAlF;QACAQ;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAP;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IACAkF;MACA;QACAnB;UACAoB;YACAhB;YACAY;YACAvB;YACAC;YACAqB;YACA1B;YACAD;UACA;UACA6B;YACAb;YACAY;YACAvB;YACAC;YACAqB;UACA;QACA;QACAtG;MACA;IACA;IACA;IACA4G;MAAA;MACApF;MACAwE,iBACAC,8BACAC;QACA1E;QACA;UACA+D;YACAe;YACAX;YACAf;YACA2B;YACA5B;UACA;UACA3E;QACA;MACA,GACAoG;QACA5E;MACA;IACA;IAEA;IACAqF;MACA;QACAtB;UACAuB;UACAP;UACArB;UACAjE;QACA;QACAjB;MACA;IACA;IAEA;IACA+G;MAAA;QAAA/G;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAqF;UACA2B;QACA;MACA;QACA3B;UACAkB;QACA;MACA;QACAlB;UACA;UACA2B;UACAT;QACA;MACA;QACAlB;UACA;UACA2B;UACAT;UACA5B;QACA;MACA;QACAU;MACA;QACAA;MACA;QACAA;MACA;MAEA;QACAA;QACA4B;QACAC;QACAC;QACAC;QACAC;QACA9B;QACA+B;UACAC;UACAC;UACAC;UACA9B;QACA;QACA+B;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC1yBA;AAAA;AAAA;AAAA;AAAm7C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAv8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/bottom-operation/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0c5cade1&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0c5cade1&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c5cade1\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/bottom-operation/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0c5cade1&scoped=true&\"", "var components\ntry {\n  components = {\n    mBottomPaceholder: function () {\n      return import(\n        /* webpackChunkName: \"components/m-bottom-paceholder/m-bottom-paceholder\" */ \"@/components/m-bottom-paceholder/m-bottom-paceholder.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.text.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <view class=\"bottom-operation-box\" @tap.stop=\"onBottom\">\r\n      <!-- <view class=\"flex_r line-break\" v-if=\"keyboardHeight\"> -->\r\n      <view class=\"flex_r line-break\" v-show=\"keyboardHeight\">\r\n        <view class=\"icon_ text_28 color__ line-break-box\" @click=\"lineBreak\">\r\n          <view class=\"icon_ line-break-icon\">\r\n            <image\r\n              class=\"img\"\r\n              src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTAgMGgxMDI0djEwMjRIMHoiIGZpbGw9IiMyYzJjMmMiIGZpbGwtb3BhY2l0eT0iLjAxIi8+PHBhdGggZD0iTTY4Mi42NjcgMTI4YTI5OC42NjcgMjk4LjY2NyAwIDAgMSAxMC4yNCA1OTcuMTYzbC0xMC4yNC4xN2gtNTEyYTQyLjY2NyA0Mi42NjcgMCAwIDEtNC45OTItODUuMDM0bDQuOTkyLS4yOTloNTEyYTIxMy4zMzMgMjEzLjMzMyAwIDAgMCA5LjI1OC00MjYuNDUzbC05LjI1OC0uMjE0aC01MTJhNDIuNjY3IDQyLjY2NyAwIDAgMS00Ljk5Mi04NS4wMzRsNC45OTItLjI5OWg1MTJ6IiBmaWxsPSIjMmMyYzJjIi8+PHBhdGggZD0iTTI0Ny4xNjggNDYwLjUwMWE0Mi42NjcgNDIuNjY3IDAgMCAxIDYzLjg3MiA1Ni4zMmwtMy41NDEgNC4wMTEtMTYwIDE2MCAxNTguMTY1IDE0MC42M2E0Mi42NjcgNDIuNjY3IDAgMCAxIDYuODI3IDU1Ljk3OGwtMy4yODYgNC4yNjdhNDIuNjY3IDQyLjY2NyAwIDAgMS01NS45NzggNi44MjZsLTQuMjY3LTMuMzI4LTE5Mi0xNzAuNjY2YTQyLjY2NyA0Mi42NjcgMCAwIDEtNS4yNDgtNTguMTU1bDMuNDEzLTMuODgzIDE5Mi0xOTJ6IiBmaWxsPSIjMmMyYzJjIi8+PC9zdmc+\"\r\n              mode=\"aspectFill\"\r\n            ></image>\r\n          </view>\r\n          换行\r\n        </view>\r\n      </view>\r\n      <view class=\"flex_r bottom-operation\">\r\n        <view class=\"icon_ bottom-operation-icon\" @click=\"onKeyboard\">\r\n          <image class=\"img\" :src=\"isKeyboard ? a : a_b\" mode=\"aspectFill\"></image>\r\n        </view>\r\n        <view style=\"width: 10rpx\"></view>\r\n        <view class=\"flex_c_c flex1\">\r\n          <view class=\"bottom-operation-input\" v-if=\"isKeyboard\">\r\n            <textarea\r\n              class=\"input\"\r\n              auto-height=\"true\"\r\n              confirm-type=\"send\"\r\n              type=\"text\"\r\n              :focus=\"isFocus\"\r\n              :maxlength=\"-1\"\r\n              :adjust-position=\"false\"\r\n              v-model=\"text\"\r\n              confirm-hold\r\n              :show-confirm-bar=\"false\"\r\n              @input=\"input\"\r\n              @confirm=\"sendingText\"\r\n              @focus=\"focus\"\r\n              @blur=\"handleBlur\"\r\n              @keyboardheightchange=\"keyboardheightchange\"\r\n            />\r\n          </view>\r\n          <!-- inputmode=\"none\" -->\r\n          <view\r\n            class=\"icon_ text_32 bold_ bottom-operation-input\"\r\n            @touchend=\"touchend\"\r\n            @touchmove=\"touchmove\"\r\n            @touchstart=\"touchstart\"\r\n            v-else\r\n          >\r\n            <view>按住</view>\r\n            <view style=\"width: 10rpx\"></view>\r\n            <view>说话</view>\r\n          </view>\r\n\r\n          <view class=\"icon_ text_26 quote\" v-if=\"isQuote\">\r\n            <view class=\"flex1 quote-row\">\r\n              <view class=\"\" v-if=\"quoteSource.type === 'image' || quoteSource.type === 'image_transmit'\">\r\n                <m-image :value=\"quoteSource\"></m-image>\r\n              </view>\r\n              <view class=\"\" v-else-if=\"quoteSource.type === 'voice'\">\r\n                <m-audio :value=\"quoteSource\"></m-audio>\r\n              </view>\r\n              <view class=\"\" v-else-if=\"quoteSource.type === 'text' || quoteSource.type === 'text_quote'\">\r\n                <m-text :value=\"quoteSource\"></m-text>\r\n              </view>\r\n              <view class=\"\" v-else>\r\n                <m-other :value=\"quoteSource\"></m-other>\r\n              </view>\r\n            </view>\r\n            <view class=\"quote-icon\" @click=\"cancelQuote\">\r\n              <image\r\n                class=\"img\"\r\n                src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTg5NS4xIDUxMi40Yy0uNi0xMDIuMy00MS0xOTguNC0xMTMuOC0yNzAuNy03Mi44LTcyLjMtMTY5LjItMTEyLTI3MS41LTExMS45LTEwMi4zLjItMTk4LjMgNDAuMi0yNzAuMyAxMTIuN1MxMjguMiA0MTEuMyAxMjguOCA1MTMuNmMuNiAxMDIuMyA0MSAxOTguNCAxMTMuOCAyNzAuN3MxNjkuMiAxMTIgMjcxLjUgMTExLjhjMTAyLjQtLjEgMTk4LjUtNDAuMSAyNzAuNC0xMTIuNiA3Mi03Mi41IDExMS4zLTE2OC43IDExMC42LTI3MS4xek02MjkgNjY3LjhsLTExNi44LTExNi0xMTYgMTE2LjhjLTEwLjcgMTAuOC0yOCAxMC44LTM4LjguMS0xMC43LTEwLjctMTAuOC0yOC0uMS0zOC44bDExNS45LTExNi44LTExNi44LTExNS45Yy0xMC43LTEwLjctMTAuOC0yOC0uMS0zOC44IDEwLjctMTAuNyAyOC0xMC44IDM4LjgtLjFsMTE2LjggMTE1LjkgMTE1LjktMTE2LjhjMTAuNy0xMC43IDI4LTEwLjggMzguOC0uMSAxMC43IDEwLjcgMTAuOCAyOCAuMSAzOC44TDU1MC44IDUxMi45bDExNi44IDExNS45YzEwLjggMTAuNyAxMC44IDI4IC4xIDM4LjgtMTAuNiAxMC44LTI4IDEwLjgtMzguNy4yem0wIDAiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTExLjFkYjQzYTgxVE1Sd1EyIiBjbGFzcz0ic2VsZWN0ZWQiIGZpbGw9IiNhMWExYTEiLz48L3N2Zz4=\"\r\n                mode=\"aspectFill\"\r\n              ></image>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view style=\"width: 10rpx\"></view>\r\n        <view class=\"icon_ bottom-operation-icon\" @click=\"tapEmoji\">\r\n          <image class=\"img\" :src=\"b\" mode=\"aspectFill\"></image>\r\n        </view>\r\n        <view class=\"icon_ bottom-operation-icon\" v-if=\"text.length\" @click=\"sendingText\">\r\n          <button class=\"send-btn\">发送</button>\r\n        </view>\r\n        <view class=\"icon_ bottom-operation-icon\" @click=\"tapMore\" v-else>\r\n          <image class=\"img\" :src=\"c\" mode=\"aspectFill\"></image>\r\n        </view>\r\n      </view>\r\n      <view>\r\n        <emoji\r\n          v-model=\"isEmoji\"\r\n          @onEmoji=\"onEmoji\"\r\n          @deleteFn=\"deleteFn\"\r\n          @sendingText=\"sendingText\"\r\n          @sendingEmojiPack=\"sendingEmojiPack\"\r\n        ></emoji>\r\n      </view>\r\n      <view>\r\n        <more v-model=\"isMore\" @onMore=\"onMore\"></more>\r\n      </view>\r\n      <!-- 键盘高度 -->\r\n      <view class=\"keyboard\" :style=\"{ height: keyboardHeight + 'px' }\"></view>\r\n      <view v-if=\"keyboardHeight === 0\">\r\n        <m-bottom-paceholder></m-bottom-paceholder>\r\n      </view>\r\n      <!-- 语音输入 -->\r\n      <m-recorder v-model=\"isRecorder\" :isCancel=\"isCancel\" @recorderTop=\"recorderTop\" @touchend=\"touchend\"></m-recorder>\r\n    </view>\r\n    <member-selection-loading\r\n      title=\"选择提醒的人\"\r\n      ref=\"memberSelectionLoadingRef\"\r\n      :group_id=\"to.id\"\r\n      @itemclick=\"itemclick\"\r\n    ></member-selection-loading>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { show, to as tofn, throttle, vibrateShortFn } from '@/utils/index.js'\r\nimport emoji from './emoji.vue'\r\nimport more from './more.vue'\r\nimport mRecorder from './m-recorder.vue'\r\nimport memberSelectionLoading from '../../../../components/memberSelection/index.vue'\r\nimport mText from '../item/quoteType/m-text.vue'\r\nimport mImage from '../item/quoteType/m-image.vue'\r\nimport mAudio from '../item/quoteType/m-audio.vue'\r\nimport mOther from '../item/quoteType/m-other.vue'\r\nimport request from '../../../../utils/request'\r\nimport { getCurrentDateTime } from '../../../../utils/index'\r\n\r\nconst recorderManager = uni.getRecorderManager()\r\n//录音时长\r\nlet startTime = 0\r\n//\r\nlet inputValue = ''\r\nlet getSelectedTextRangeSetInterval = null\r\nlet cursor = 0 //输入框光标\r\nexport default {\r\n  components: {\r\n    emoji,\r\n    more,\r\n    mRecorder,\r\n    // memberSelection,\r\n    memberSelectionLoading,\r\n    mText,\r\n    mImage,\r\n    mAudio,\r\n    mOther\r\n  },\r\n  props: {\r\n    to: {\r\n      type: Object,\r\n      default: {}\r\n    },\r\n    userInfo: {\r\n      type: Object,\r\n      default: {}\r\n    },\r\n    groupInfo: {\r\n      type: Object,\r\n      default: {}\r\n    },\r\n    userList: {\r\n      type: Array,\r\n      default: () => {\r\n        return []\r\n      }\r\n    },\r\n    isPrivate: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      a: 'data:image/svg+xml;base64,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',\r\n      a_b: 'data:image/svg+xml;base64,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',\r\n      b: 'data:image/svg+xml;base64,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',\r\n      c: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMS4zIDk1OS43Yy02MC40IDAtMTE5LTExLjgtMTc0LjItMzUuMi01My4zLTIyLjUtMTAxLjEtNTQuOC0xNDIuMi05NS45LTQxLjEtNDEuMS03My40LTg4LjktOTUuOS0xNDIuMi0yMy4zLTU1LjItMzUuMi0xMTMuOC0zNS4yLTE3NC4yUzc1LjYgMzkzLjEgOTkgMzM4YzIyLjUtNTMuMyA1NC44LTEwMS4xIDk1LjktMTQyLjIgNDEuMS00MS4xIDg4LjktNzMuNCAxNDIuMi05NS45IDU1LjItMjMuMyAxMTMuOC0zNS4yIDE3NC4yLTM1LjIgNjAuNCAwIDExOSAxMS44IDE3NC4yIDM1LjIgNTMuMyAyMi41IDEwMS4xIDU0LjggMTQyLjIgOTUuOSA0MS4xIDQxLjEgNzMuNCA4OC45IDk1LjkgMTQyLjIgMjMuMyA1NS4yIDM1LjIgMTEzLjggMzUuMiAxNzQuMnMtMTEuOCAxMTktMzUuMiAxNzQuMmMtMjIuNSA1My4zLTU0LjggMTAxLjEtOTUuOSAxNDIuMi00MS4xIDQxLjEtODguOSA3My40LTE0Mi4yIDk1LjktNTUuMiAyMy4zLTExMy44IDM1LjItMTc0LjIgMzUuMnptMC04MzljLTUyLjkgMC0xMDQuMSAxMC4zLTE1Mi40IDMwLjgtNDYuNiAxOS43LTg4LjUgNDcuOS0xMjQuNSA4My45LTM2IDM2LTY0LjIgNzcuOC04My45IDEyNC41LTIwLjQgNDguMi0zMC44IDk5LjUtMzAuOCAxNTIuNHMxMC4zIDEwNC4xIDMwLjggMTUyLjRjMTkuNyA0Ni42IDQ3LjkgODguNSA4My45IDEyNC41IDM2IDM2IDc3LjggNjQuMiAxMjQuNSA4My45IDQ4LjIgMjAuNCA5OS41IDMwLjggMTUyLjQgMzAuOCA1Mi45IDAgMTA0LjEtMTAuMyAxNTIuNC0zMC44IDQ2LjYtMTkuNyA4OC41LTQ3LjkgMTI0LjUtODMuOXM2NC4yLTc3LjggODMuOS0xMjQuNWMyMC40LTQ4LjIgMzAuOC05OS41IDMwLjgtMTUyLjRTODkyLjQgNDA4IDg3MiAzNTkuOGMtMTkuNy00Ni42LTQ3LjktODguNS04My45LTEyNC41cy03Ny44LTY0LjItMTI0LjUtODMuOWMtNDguMi0yMC40LTk5LjUtMzAuNy0xNTIuMy0zMC43eiIvPjxwYXRoIGQ9Ik03MzcuMyA0ODQuMmgtMTk4di0xOThjMC0xNS41LTEyLjUtMjgtMjgtMjhzLTI4IDEyLjUtMjggMjh2MTk4aC0xOThjLTE1LjUgMC0yOCAxMi41LTI4IDI4czEyLjUgMjggMjggMjhoMTk4djE5OGMwIDE1LjUgMTIuNSAyOCAyOCAyOHMyOC0xMi41IDI4LTI4di0xOThoMTk4YzE1LjUgMCAyOC0xMi41IDI4LTI4cy0xMi42LTI4LTI4LTI4eiIvPjwvc3ZnPg==',\r\n      isFocus: false, //键盘焦点\r\n      isKeyboard: true,\r\n      isEmoji: false,\r\n      isMore: false,\r\n      isRecorder: false,\r\n      isCancel: false, //是否滑动到取消\r\n      text: '',\r\n      keyboardHeight: 0,\r\n      isQuote: false, //是否引用\r\n      quoteSource: {}, //引用的源\r\n      keyHeight: 0\r\n    }\r\n  },\r\n  created() {\r\n    this.initRecorderListeners()\r\n    // 监听设置群公告\r\n    uni.$off('getNoticeSendMessage', this.sendMessage)\r\n    uni.$on('getNoticeSendMessage', this.sendMessage)\r\n\r\n    // 监听修改群明\r\n    uni.$off('getGroupNameMessage', this.sendMessage)\r\n    uni.$on('getGroupNameMessage', this.sendMessage)\r\n\r\n    // #ifdef MP-WEIXIN\r\n    uni.onKeyboardHeightChange((res) => {\r\n      console.log('res.height', res.height)\r\n      this.keyHeight = res.height\r\n    })\r\n\r\n    // #endif\r\n  },\r\n  beforeDestroy() {\r\n    uni.$off('getNoticeSendMessage', this.sendMessage)\r\n    uni.$off('getGroupNameMessage', this.sendMessage)\r\n    cursor = 0\r\n    clearInterval(getSelectedTextRangeSetInterval)\r\n  },\r\n  methods: {\r\n    setCursor() {\r\n      getSelectedTextRangeSetInterval = setInterval(() => {\r\n        uni.getSelectedTextRange({\r\n          success: (res) => {\r\n            cursor = res.start\r\n          },\r\n          fail: () => {\r\n            clearInterval(getSelectedTextRangeSetInterval)\r\n          }\r\n        })\r\n      }, 800)\r\n    },\r\n    // 滚动到底部\r\n    backToBottom() {\r\n      this.$emit('backToBottom')\r\n    },\r\n    // 关闭全部弹出/输入框/表情包\r\n    closeAll() {\r\n      this.isMore = false\r\n      this.isEmoji = false\r\n      this.isFocus = false\r\n    },\r\n\r\n    onBottom() {\r\n      this.$emit('onBottom')\r\n    },\r\n    // 重新编辑\r\n    recalledEdit(item) {\r\n      this.text = item.payload.text\r\n      this.$nextTick(() => {\r\n        this.isFocus = true\r\n      })\r\n    },\r\n    // 关闭\r\n    close() {\r\n      this.isMore = false\r\n      this.isEmoji = false\r\n      // #ifdef H5\r\n      this.keyboardHeight = 0\r\n      // #endif\r\n      this.$emit('keyboardheightchange', 0)\r\n    },\r\n    // 切换语音输入\r\n    onKeyboard() {\r\n      this.isKeyboard = !this.isKeyboard\r\n      this.isMore = false\r\n      this.isEmoji = false\r\n    },\r\n    keyboardheightchange(e) {\r\n      if (e.detail.duration > 0) {\r\n        this.backToBottom()\r\n      }\r\n      // #ifdef APP || H5\r\n      let height = e.detail.height\r\n      this.keyboardHeight = height\r\n      if (height > 0) {\r\n        const res = uni.getSystemInfoSync()\r\n        let bottom = res.safeAreaInsets.bottom\r\n        height -= bottom\r\n        height -= uni.upx2px(20) //补偿高度\r\n      }\r\n      this.isMore = false\r\n      this.isEmoji = false\r\n      this.$emit('keyboardheightchange', height, true)\r\n      // #endif\r\n\r\n      // #ifdef MP\r\n      if (e.detail.duration > 0) {\r\n        throttle(() => {\r\n          this.keyboardHeight = e.detail.height\r\n          this.isMore = false\r\n          this.isEmoji = false\r\n          this.$emit('keyboardheightchange', this.keyboardHeight)\r\n        }, 300)\r\n      }\r\n      // #endif\r\n    },\r\n    tapEmoji() {\r\n      this.backToBottom()\r\n      this.isEmoji = !this.isEmoji\r\n      if (this.isEmoji) {\r\n        this.isKeyboard = true\r\n      }\r\n      this.isMore = false\r\n      // #ifdef H5\r\n      this.keyboardHeight = 0\r\n      // #endif\r\n      this.$emit('keyboardheightchange', uni.upx2px(690))\r\n    },\r\n    tapMore() {\r\n      this.backToBottom()\r\n      this.isMore = !this.isMore\r\n      this.isEmoji = false\r\n      // #ifdef H5\r\n      this.keyboardHeight = 0\r\n      // #endif\r\n      this.$emit('keyboardheightchange', uni.upx2px(430))\r\n    },\r\n    onEmoji(key) {\r\n      const text = `${this.text.slice(0, cursor)}${key}${this.text.slice(cursor)}`\r\n      this.text = text\r\n    },\r\n    // ===========================\r\n    // 获取焦点\r\n    focus(e) {\r\n      this.$emit('focus')\r\n      this.isFocus = true\r\n      this.isEmoji = false\r\n      this.isMore = false\r\n      // #ifdef H5\r\n      this.keyboardHeight = 300\r\n      this.$emit('keyboardheightchange', this.keyboardHeight)\r\n      this.backToBottom()\r\n      // #endif\r\n      // #ifdef MP-WEIXIN\r\n      this.keyboardHeight = this.keyHeight\r\n      this.$emit('keyboardheightchange', this.keyboardHeight)\r\n      this.backToBottom()\r\n      // #endif\r\n      clearInterval(getSelectedTextRangeSetInterval)\r\n      this.setCursor()\r\n    },\r\n    handleBlur() {\r\n      this.isFocus = false\r\n      this.keyboardHeight = 0\r\n      this.$emit('keyboardheightchange', this.keyboardHeight)\r\n      this.backToBottom()\r\n    },\r\n    // 监听输入,\r\n    input() {\r\n      if (inputValue.length > this.text.length) {\r\n      } else {\r\n        const str = this.text.charAt(this.text.length - 1)\r\n        if (str === '@') {\r\n          if (this.isFocus === false) return\r\n          this.$refs.memberSelectionLoadingRef.open()\r\n          this.$nextTick(() => {\r\n            this.isFocus = false\r\n          })\r\n        }\r\n      }\r\n      inputValue = this.text\r\n    },\r\n    // 插入换行符合\r\n    lineBreak() {\r\n      console.log('回车')\r\n      console.log(cursor)\r\n      const text = `${this.text.slice(0, cursor)}\\n${this.text.slice(cursor)}`\r\n      this.text = text\r\n      // this.text = `${this.text}\\r\\n`;\r\n      this.$nextTick(() => {\r\n        this.isFocus = true\r\n      })\r\n    },\r\n    // 输入@某个成员\r\n    itemclick(item) {\r\n      if (item) {\r\n        this.text = `${this.text}${item.name} `\r\n      }\r\n      this.$nextTick(() => {\r\n        this.isFocus = true\r\n      })\r\n    },\r\n    // 删除表情\r\n    deleteFn() {\r\n      const str = this.text.charAt(this.text.length - 1)\r\n      if (str === ']') {\r\n        let metaChars = /\\[.*?(\\u4e00*\\u597d*)\\]/g\r\n        let xstr = ''\r\n        this.text.replace(metaChars, (match) => {\r\n          xstr = match\r\n        })\r\n        var text = this.text\r\n\r\n        function del(str) {\r\n          return text.slice(0, text.length - str.length)\r\n        }\r\n        this.text = del(xstr)\r\n      } else {\r\n        this.text = this.text.substring(0, this.text.length - 1)\r\n      }\r\n    },\r\n    // 引用\r\n    quote(item) {\r\n      // 删除嵌套引用\r\n      const itemx = JSON.parse(JSON.stringify(item))\r\n      itemx.payload['quoteSource'] = {}\r\n      this.isQuote = true\r\n      this.quoteSource = itemx\r\n      this.$nextTick(() => {\r\n        this.isFocus = true\r\n      })\r\n    },\r\n    //谢谢红包\r\n    thank(item) {\r\n      this.text = '[彩带][玫瑰]谢谢红包！'\r\n      this.sendingText()\r\n    },\r\n    //长按@某人\r\n    mention(item) {\r\n      this.text = `${this.text}@${item.senderData.name} `\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isFocus = true\r\n        }, 500)\r\n      })\r\n      try {\r\n        vibrateShortFn()\r\n      } catch (e) {\r\n        //TODO handle the exception\r\n      }\r\n    },\r\n    cancelQuote() {\r\n      this.isQuote = false\r\n    },\r\n\r\n    // 录音相关===============\r\n    recorderTop(e) {\r\n      this.recorderTopValue = e?.top\r\n    },\r\n    initRecorderListeners() {\r\n      // 监听录音开始\r\n      recorderManager.onStart(() => {\r\n        // console.log('开始录音');\r\n        startTime = Date.now()\r\n      })\r\n      //录音结束后，发送\r\n      recorderManager.onStop((res) => {\r\n        this.isRecorder = false\r\n        if (this.isCancel) return console.log('取消发送') //取消发送\r\n        let endTime = Date.now()\r\n        let duration = endTime - startTime\r\n        if (duration < 1000) return show('录音时间太短', 1000, 'error')\r\n        res.duration = duration\r\n        // 创建信息\r\n        this.sendingRecorder(res)\r\n      })\r\n      // 监听录音报错\r\n      recorderManager.onError((res) => {\r\n        this.isRecorder = false\r\n        recorderManager.stop()\r\n        show('请检查麦克风权限')\r\n      })\r\n    },\r\n    // 按下\r\n    touchstart() {\r\n      this.isRecorder = true\r\n      this.isCancel = false\r\n      try {\r\n        recorderManager.start({ format: 'mp3' })\r\n      } catch (e) {\r\n        show('H5不支持')\r\n      }\r\n    },\r\n    // 拖拽中\r\n    touchmove(e) {\r\n      let touch = e.touches[0] //滑动过程中，手指滑动的坐标信息 返回的是Objcet对象\r\n      if (touch.clientY <= this.recorderTopValue) {\r\n        // 取消发送\r\n        this.isCancel = true\r\n      } else {\r\n        this.isCancel = false\r\n      }\r\n    },\r\n    // 松手\r\n    touchend() {\r\n      try {\r\n        recorderManager.stop()\r\n      } catch (e) {\r\n        console.log('e:', e)\r\n      }\r\n      this.isRecorder = false\r\n    },\r\n    // ===================\r\n    // 更多操作相关===============\r\n    onMore(item) {\r\n      switch (item.type) {\r\n        // 拍摄\r\n        case 'shot':\r\n          this.openShot()\r\n          break\r\n        case 'image':\r\n          this.sendImageMessage()\r\n          break\r\n        case 'video':\r\n          this.sendVideoMessage()\r\n          break\r\n        case 'red_envelope':\r\n          // 发红包\r\n          uni.$off('send_red_envelope', this.sendMessage)\r\n          uni.$on('send_red_envelope', this.sendMessage)\r\n\r\n          // 是否是单聊\r\n          if (this.isPrivate) {\r\n            tofn('/pagesGoEasy/envelope_sending/index-private', {\r\n              ...this.to\r\n            })\r\n          } else {\r\n            tofn('/pagesGoEasy/envelope_sending/index', {\r\n              ...this.to\r\n            })\r\n          }\r\n          break\r\n        case 'mutualism':\r\n          // 蝌蚪互转\r\n          tofn('/pagesThree/tadpoleChange/index?type=1')\r\n          break\r\n        case 'map':\r\n          // 位置\r\n          uni.chooseLocation({\r\n            success: async (res) => {\r\n              console.log(res)\r\n              uni.showLoading({\r\n                title: '发送中'\r\n              })\r\n              if (res2) {\r\n                this.createCustomMessageMap(res, 'http://xxxxxxxx/map/staticMap?location=116.459044,39.918732&size=300*170')\r\n              }\r\n              uni.hideLoading()\r\n            },\r\n            fail(e) {\r\n              console.log(e)\r\n            }\r\n          })\r\n          break\r\n        default:\r\n          break\r\n      }\r\n    },\r\n    openShot() {\r\n      show('这个用的是原生插件，Html5App-CameraView')\r\n      return\r\n\r\n      const plug = uni.requireNativePlugin('Html5App-CameraView')\r\n      plug.open(\r\n        {\r\n          setMaxduration: 30,\r\n          SpeedColor: '#05c160',\r\n          ratio: '9/16'\r\n        },\r\n        (retult) => {\r\n          const { type, mp4 = '', duration = '', size = '', image } = retult\r\n          if (type == 'video') {\r\n            const file = {\r\n              errMsg: 'chooseVideo:ok',\r\n              tempFilePath: mp4,\r\n              size: Number(size) * 1000,\r\n              duration: duration, //视频时间\r\n              width: 360,\r\n              height: 640\r\n            }\r\n            this.createVideoMessage(file)\r\n          } else if (type == 'image') {\r\n            this.createImageMessage({\r\n              size: Number(size) * 1000,\r\n              path: image\r\n            })\r\n          }\r\n          //用户取消拍摄\r\n          if (retult.retult == 'cancel') {\r\n          }\r\n        }\r\n      )\r\n    },\r\n    // =====================\r\n    // 创建发送输入框内容\r\n    sendingText() {\r\n      if (this.text === '')\r\n        return uni.showModal({\r\n          showCancel: false,\r\n          content: '不能发送空白信息',\r\n          success: function (res) {}\r\n        })\r\n      let body = this.text\r\n      if (this.text.length >= 50) {\r\n        body = this.text.substring(0, 30) + '...'\r\n      }\r\n      if (this.isQuote) {\r\n        this.createCustomMessageText(body)\r\n        return\r\n      }\r\n      this.sendMessage({\r\n        payload: {\r\n          text: this.text\r\n        },\r\n        type: 'text'\r\n      })\r\n\r\n      this.text = ''\r\n    },\r\n\r\n    // 发送位置信息\r\n    createCustomMessageMap(res, image) {\r\n      const { latitude, longitude, address, name } = res\r\n      this.sendMessage({\r\n        payload: {\r\n          latitude,\r\n          longitude,\r\n          title: name,\r\n          address,\r\n          image //使用高德api生成图片\r\n        },\r\n\r\n        type: 'map'\r\n      })\r\n    },\r\n\r\n    // 引用并发送文本\r\n    createCustomMessageText(body) {\r\n      this.sendMessage({\r\n        payload: {\r\n          text: this.text,\r\n          //引用源\r\n          quoteSource: {\r\n            ...this.quoteSource\r\n          }\r\n        },\r\n        type: 'text_quote'\r\n      })\r\n\r\n      this.text = ''\r\n    },\r\n\r\n    // 创建发送照片内容\r\n    sendImageMessage() {\r\n      uni.chooseImage({\r\n        count: 9,\r\n        success: async (res) => {\r\n          res.tempFiles.forEach((file) => {\r\n            console.log('🚀 ~ sendImageMessage ~ file:', file)\r\n            request\r\n              .uploadFile(file.path)\r\n              .then((data) => {\r\n                console.log('上传成功:', data)\r\n                file.path = data\r\n                this.createImageMessage(data)\r\n              })\r\n              .catch((error) => {\r\n                console.error('上传失败:', error)\r\n              })\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 创建发送照片内容\r\n    createImageMessage(file) {\r\n      console.log('🚀 ~ createImageMessage ~ file:', file)\r\n      this.sendMessage({\r\n        payload: {\r\n          contentType: 'image/png',\r\n          name: 'uni-image.png',\r\n          size: 82942,\r\n          url: file,\r\n          width: 2732,\r\n          height: 2732,\r\n          thumbnail: file\r\n        },\r\n        type: 'image'\r\n      })\r\n    },\r\n    // 创建发送视频内容\r\n    sendVideoMessage() {\r\n      uni.chooseVideo({\r\n        success: async (res) => {\r\n          console.log(res)\r\n          this.createVideoMessage(res)\r\n        }\r\n      })\r\n    },\r\n    createVideoMessage(file) {\r\n      this.sendMessage({\r\n        payload: {\r\n          video: {\r\n            name: '3003009356267921_uni-video.mp4',\r\n            url: file.tempFilePath,\r\n            width: 640,\r\n            height: 352,\r\n            contentType: 'video/mp4',\r\n            size: 501774,\r\n            duration: 8.32\r\n          },\r\n          thumbnail: {\r\n            name: 'uni-thumbnail.jpg',\r\n            url: '封面路径',\r\n            width: 364,\r\n            height: 200,\r\n            contentType: 'image/jpg'\r\n          }\r\n        },\r\n        type: 'video'\r\n      })\r\n    },\r\n    // 创建语音内容\r\n    sendingRecorder(file) {\r\n      console.log('🚀 ~ sendingRecorder ~ file:', file)\r\n      request\r\n        .uploadFile(file.tempFilePath)\r\n        .then((data) => {\r\n          console.log('🚀 ~ sendingRecorder ~ data:', data)\r\n          this.sendMessage({\r\n            payload: {\r\n              contentType: 'voice/mp3',\r\n              name: '语音.mp3',\r\n              size: 2357,\r\n              url: data,\r\n              duration: Math.ceil(file.duration / 1000)\r\n            },\r\n            type: 'voice'\r\n          })\r\n        })\r\n        .catch((error) => {\r\n          console.error('上传失败:', error)\r\n        })\r\n    },\r\n\r\n    // 创建自定义表情包\r\n    sendingEmojiPack(e) {\r\n      this.sendMessage({\r\n        payload: {\r\n          ext: e.ext,\r\n          url: e.url,\r\n          path: e.path,\r\n          text: e.text || '[表情包]'\r\n        },\r\n        type: 'emoji_pack'\r\n      })\r\n    },\r\n\r\n    // 最终提交发送\r\n    sendMessage({ payload, type }) {\r\n      // 获取当前时间\r\n      const createTime = getCurrentDateTime()\r\n      // 生成消息ID\r\n      const messageId = `${this.groupInfo.id}_${Date.now().toString().slice(-6)}`\r\n      // 生成消息内容\r\n      let content = {}\r\n      if (type === 'text') {\r\n        content = {\r\n          msg: payload.text\r\n        }\r\n      } else if (type === 'image') {\r\n        content = {\r\n          url: payload.url\r\n        }\r\n      } else if (type === 'video') {\r\n        content = {\r\n          //向上取整\r\n          msg: `语音 ${Math.ceil(payload.duration / 1000)}`,\r\n          url: payload.url\r\n        }\r\n      } else if (type === 'voice') {\r\n        content = {\r\n          //向上取整\r\n          msg: `语音 ${payload.duration}`,\r\n          url: payload.url,\r\n          duration: payload.duration\r\n        }\r\n      } else if (type === 'map') {\r\n        content = payload.title\r\n      } else if (type === 'emoji_pack') {\r\n        content = payload.text\r\n      } else if (type === 'text_quote') {\r\n        content = payload.text\r\n      }\r\n\r\n      const message = {\r\n        content,\r\n        createBy: null,\r\n        createTime: createTime,\r\n        groupId: this.groupInfo.id,\r\n        id: messageId,\r\n        msgType: type,\r\n        payload: payload,\r\n        senderData: {\r\n          avatar: this.userInfo.avatar || '',\r\n          group_id: this.groupInfo.id,\r\n          member_id: this.userInfo.userId,\r\n          name: this.userInfo.name || this.userInfo.nickname || ''\r\n        },\r\n        status: '正常',\r\n        sysOrgCode: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: this.userInfo.userId,\r\n        recalled: false,\r\n        isHide: 0\r\n      }\r\n      this.$emit('pushList', message)\r\n      this.isQuote = false\r\n      this.quoteSource = {}\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bottom-operation-box {\r\n  position: relative;\r\n  z-index: 9999;\r\n  width: 100vw;\r\n  background-color: #f6f6f6;\r\n\r\n  .line-break {\r\n    position: absolute;\r\n    z-index: 99;\r\n    left: 0;\r\n    top: -58rpx;\r\n    width: 100%;\r\n    height: 60rpx;\r\n    flex-direction: row-reverse;\r\n\r\n    .line-break-box {\r\n      position: relative;\r\n      width: 160rpx;\r\n      height: 100%;\r\n      color: #2c2c2c;\r\n      border-radius: 20rpx 0 0 0;\r\n      background-color: #f6f6f6;\r\n\r\n      .line-break-icon {\r\n        width: 36rpx;\r\n        height: 36rpx;\r\n        margin-right: 10rpx;\r\n      }\r\n    }\r\n\r\n    .line-break-box::before {\r\n      position: absolute;\r\n      left: -60rpx;\r\n      top: 0;\r\n      content: '';\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      display: block;\r\n      text-align: center;\r\n      background-image: radial-gradient(240rpx at 2rpx 0px, rgba(168, 195, 59, 0) 60rpx, #f6f6f6 60rpx);\r\n    }\r\n  }\r\n}\r\n\r\n.bottom-operation {\r\n  box-sizing: border-box;\r\n  padding: 14rpx 10rpx;\r\n  width: 100%;\r\n  align-items: flex-end;\r\n\r\n  .bottom-operation-icon {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n\r\n    .img {\r\n      width: 80%;\r\n      height: 80%;\r\n    }\r\n  }\r\n  .send-btn {\r\n    color: white;\r\n    background-color: #60be60;\r\n    border-radius: 8rpx;\r\n    margin: 0;\r\n    line-height: normal;\r\n    font-size: 28rpx;\r\n    padding: 8rpx 12rpx;\r\n  }\r\n\r\n  .bottom-operation-input {\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    padding: 10rpx 14rpx;\r\n    min-height: 84rpx;\r\n    max-height: 300rpx;\r\n    overflow: auto;\r\n    border-radius: 10rpx;\r\n    background-color: #fff;\r\n\r\n    .input {\r\n      width: 100%;\r\n      margin: 10rpx 0;\r\n    }\r\n  }\r\n}\r\n\r\n.keyboard {\r\n  transition: all 0.2s;\r\n}\r\n\r\n// 引用\r\n.quote {\r\n  box-sizing: border-box;\r\n  padding: 0 20rpx;\r\n  width: 100%;\r\n  height: 50rpx;\r\n  margin-top: 8rpx;\r\n  border-radius: 10rpx;\r\n  background-color: #eaeaea;\r\n  color: #686868;\r\n\r\n  .quote-row {\r\n    width: 200rpx;\r\n    text-overflow: ellipsis;\r\n    overflow: auto;\r\n    white-space: nowrap;\r\n\r\n    ::v-deep .quote-box {\r\n      width: 100%;\r\n      box-sizing: border-box;\r\n      padding: 0;\r\n      border-radius: 0;\r\n      margin-top: 0;\r\n      background-color: #eaeaea;\r\n      color: #6b6b6b;\r\n\r\n      .quote-name {\r\n      }\r\n\r\n      .m-image {\r\n        border-radius: 6rpx;\r\n        overflow: hidden;\r\n\r\n        .img {\r\n          width: 40rpx;\r\n          height: 40rpx;\r\n          border-radius: 6rpx;\r\n          overflow: hidden;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .quote-icon {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0c5cade1&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0c5cade1&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755156565961\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}