{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/components/m-line/m-line.vue?8ba6", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/components/m-line/m-line.vue?7625", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/components/m-line/m-line.vue?80e8", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/components/m-line/m-line.vue?bf88", "uni-app:///components/m-line/m-line.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/components/m-line/m-line.vue?475a", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/components/m-line/m-line.vue?04e8"], "names": ["name", "props", "color", "type", "default", "length", "direction", "hairline", "margin", "marginTop", "dashed", "computed", "lineStyle", "style"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA4sB,CAAgB,4qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;eCShuB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;EACA;EACAO;IACAC;MACA;MACAC;MACAA;MACA;MACA;QACA;QACAA;QACAA;QACAA;QACA;MACA;QACA;QACAA;QACAA;QACAA;QACA;MACA;MACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAm2C,CAAgB,muCAAG,EAAC,C;;;;;;;;;;;ACAv3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/m-line/m-line.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-line.vue?vue&type=template&id=318dac78&scoped=true&\"\nvar renderjs\nimport script from \"./m-line.vue?vue&type=script&lang=js&\"\nexport * from \"./m-line.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-line.vue?vue&type=style&index=0&id=318dac78&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"318dac78\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/m-line/m-line.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-line.vue?vue&type=template&id=318dac78&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.lineStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-line.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-line.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t    class=\"u-line\"\r\n\t    :style=\"[lineStyle]\"\r\n\t>\r\n\t</view>\r\n</template>\r\n<!-- <m-line color=\"#B8B8B8\" length=\"186rpx\" :hairline=\"true\"></m-line> -->\r\n<script>\r\n\texport default {\r\n\t\tname: 'm-line',\r\n\t\tprops: {\r\n\t\t    color: {\r\n\t\t        type: String,\r\n\t\t        default: \"#BBBBBB\"\r\n\t\t    },\r\n\t\t    // 长度，竖向时表现为高度，横向时表现为长度，可以为百分比，带px单位的值等\r\n\t\t    length: {\r\n\t\t        type: [String, Number],\r\n\t\t        default: \"100%\"\r\n\t\t    },\r\n\t\t    // 线条方向，col-竖向，row-横向\r\n\t\t    direction: {\r\n\t\t        type: String,\r\n\t\t        default: \"row\"\r\n\t\t    },\r\n\t\t    // 是否显示细边框\r\n\t\t    hairline: {\r\n\t\t        type: Boolean,\r\n\t\t        default: true\r\n\t\t    },\r\n\t\t    // 线条与上下左右元素的间距，字符串形式，如\"30px\"、\"20px 30px\"\r\n\t\t    margin: {\r\n\t\t        type: [String, Number],\r\n\t\t        default: \"0px\"\r\n\t\t    },\r\n\t\t\tmarginTop:{\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: \"0px\"\r\n\t\t\t},\r\n\t\t    // 是否虚线，true-实线，false-虚线\r\n\t\t    dashed: {\r\n\t\t        type: <PERSON><PERSON>an,\r\n\t\t        default: false\r\n\t\t    }\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tlineStyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\tstyle.marginTop = this.marginTop\r\n\t\t\t\tstyle.margin = this.margin\r\n\t\t\t\t// 如果是水平线条，边框高度为1px，再通过transform缩小一半，就是0.5px了\r\n\t\t\t\tif (this.direction === 'row') {\r\n\t\t\t\t\t// 此处采用兼容分开写，兼容nvue的写法\r\n\t\t\t\t\tstyle.borderBottomWidth = '1px'\r\n\t\t\t\t\tstyle.borderBottomStyle = this.dashed ? 'dashed' : 'solid'\r\n\t\t\t\t\tstyle.width = this.length\r\n\t\t\t\t\tif (this.hairline) style.transform = 'scaleY(0.5)'\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果是竖向线条，边框宽度为1px，再通过transform缩小一半，就是0.5px了\r\n\t\t\t\t\tstyle.borderLeftWidth = '1px'\r\n\t\t\t\t\tstyle.borderLeftStyle = this.dashed ? 'dashed' : 'solid'\r\n\t\t\t\t\tstyle.height = this.length\r\n\t\t\t\t\tif (this.hairline) style.transform = 'scaleX(0.5)'\r\n\t\t\t\t}\r\n\t\t\t\tstyle.borderColor = this.color\r\n\t\t\t\treturn style;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.u-line {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tvertical-align: middle;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-line.vue?vue&type=style&index=0&id=318dac78&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-line.vue?vue&type=style&index=0&id=318dac78&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755156565764\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}