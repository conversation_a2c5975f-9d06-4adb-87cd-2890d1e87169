<view class="data-v-4a000799"><view style="height:20rpx;" class="data-v-4a000799"></view><view class="flex_r fa_c infor data-v-4a000799"><view data-event-opts="{{[['tap',[['openimg',['$0'],['senderData.avatar']]]]]}}" class="infor-img-box data-v-4a000799" bindtap="__e"><block wx:if="{{senderData.avatar}}"><view class="avatar_pendant data-v-4a000799"><image class="img data-v-4a000799" src="{{senderData.avatar}}" mode="aspectFill"></image></view></block><view class="infor-img data-v-4a000799"><image class="img data-v-4a000799" src mode="aspectFill"></image></view></view><view class="flex1 infor-r data-v-4a000799"><view class="flex_r fa_c infor-r-name data-v-4a000799"><view class="text_34 bold_ data-v-4a000799">{{senderData.nickname}}</view></view><view class="text_26 color__ data-v-4a000799">{{"群昵称："+groupInfo.title}}</view><view class="text_26 color__ data-v-4a000799">{{"ID："+senderData.userId}}</view></view></view></view>